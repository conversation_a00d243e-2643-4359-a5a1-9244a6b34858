<!doctype html>
<html lang="{{ $languageShort }}" dir="{{ $languageDir }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    {{-- SEO Meta Tags from Settings --}}
    @if (isset($front) && $front->meta_title)
        <title>{{ $front->meta_title }}</title>
        <meta name="description" content="{{ $front->meta_description }}">
        <meta name="keywords" content="{{ $front->meta_keywords }}">
    @else
        <title>{{ $meta_title ?? $site_name }}</title>
        <meta name="description" content="{{ $meta_description ?? $site_description }}">
        <meta name="keywords" content="{{ $meta_keywords }}">
    @endif

    {{-- Open Graph Meta Tags --}}
    <meta property="og:title" content="{{ (isset($front) && $front->meta_title) ? $front->meta_title : ($meta_title ?? $site_name) }}">
    <meta property="og:description" content="{{ (isset($front) && $front->meta_description) ? $front->meta_description : ($meta_description ?? $site_description) }}">
    <meta property="og:image" content="{{ $og_image }}">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ $site_name }}">

    {{-- Twitter Card Meta Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ (isset($front) && $front->meta_title) ? $front->meta_title : ($meta_title ?? $site_name) }}">
    <meta name="twitter:description" content="{{ (isset($front) && $front->meta_description) ? $front->meta_description : ($meta_description ?? $site_description) }}">
    <meta name="twitter:image" content="{{ $og_image }}">

    {{-- Additional Meta Tags --}}
    <meta name="author" content="{{ $site_name }}">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ url()->current() }}">

    {{-- Favicon --}}
    <link rel="icon" type="image/x-icon" href="{{ $site_icon }}">
    <link rel="shortcut icon" href="{{ $site_icon }}">
    <link rel="apple-touch-icon" href="{{ $site_icon }}">

    <!-- Favicon -->
    @php
        $faviconUrl = $site_icon ?? asset('favicon.ico');
        $faviconExtension = pathinfo($faviconUrl, PATHINFO_EXTENSION);
        $faviconType = match(strtolower($faviconExtension)) {
            'png' => 'image/png',
            'jpg', 'jpeg' => 'image/jpeg',
            'svg' => 'image/svg+xml',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            default => 'image/x-icon'
        };
    @endphp

    @if (isset($site_icon) && $site_icon)
        <!-- Primary favicon -->
        <link rel="icon" type="{{ $faviconType }}" href="{{ $site_icon }}">
        <link rel="shortcut icon" type="{{ $faviconType }}" href="{{ $site_icon }}">

        <!-- Apple touch icon -->
        <link rel="apple-touch-icon" href="{{ $site_icon }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ $site_icon }}">

        <!-- Different sizes for PNG -->
        @if($faviconType === 'image/png')
            <link rel="icon" type="image/png" sizes="32x32" href="{{ $site_icon }}">
            <link rel="icon" type="image/png" sizes="16x16" href="{{ $site_icon }}">
            <link rel="icon" type="image/png" sizes="96x96" href="{{ $site_icon }}">
        @endif

        <!-- Manifest for PWA -->
        <meta name="msapplication-TileImage" content="{{ $site_icon }}">
    @else
        <!-- Default favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
        <link rel="shortcut icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
        <link rel="apple-touch-icon" href="{{ asset('favicon.ico') }}">
    @endif

    <!-- DNS Prefetch and Preconnect for performance -->
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Critical CSS only -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Load other CSS asynchronously -->
    <script>
        function loadCSS(href){
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
        }
        setTimeout(function(){
            loadCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
            loadCSS('https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css');
            loadCSS('https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css');
        }, 50);
    </script>

    <!-- Arabic Fonts - Load after page load -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <script>
        window.addEventListener('load', function(){
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&family=Tajawal:wght@400;500;700&display=swap';
            document.head.appendChild(link);
        });
    </script>

    <!-- Critical Custom CSS only -->
    <link rel="stylesheet" href="{{ asset('assets/v1/css/style.css') }}">

    <!-- Load other custom CSS after page load -->
    <script>
        window.addEventListener('load', function(){
            var cssFiles = [
                '{{ asset('assets/css/modern-homepage.css') }}',
                '{{ asset('assets/css/simple-language-switcher.css') }}',
                '{{ asset('assets/css/modern-about-section.css') }}',
                '{{ asset('assets/css/modern-pricing-section.css') }}',
                '{{ asset('assets/css/modern-features-section.css') }}',
                '{{ asset('assets/css/modern-testimonials-section.css') }}',
                '{{ asset('assets/css/modern-faq-section.css') }}',
                '{{ asset('assets/css/modern-cta-section.css') }}',
                '{{ asset('assets/css/modern-contact-section.css') }}'
            ];
            cssFiles.forEach(function(href){
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = href;
                document.head.appendChild(link);
            });
        });
    </script>

    @livewireStyles

    <!-- Custom Navbar CSS -->
    <style>
        :root {
            --primary-color: {{ $front->primary_color ?? '#007bff' }};
            --secondary-color: {{ $front->secondary_color ?? '#6c757d' }};
            --accent-color: {{ $front->accent_color ?? '#28a745' }};
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            direction: {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }};
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        /* منع كسر النص في النافبار */
        .navbar-nav .nav-link {
            white-space: nowrap !important;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            font-size: 0.95rem;
        }

        /* تحسين النافبار للشاشات المتوسطة */
        @media (max-width: 1199.98px) {
            .navbar-nav .nav-link {
                max-width: 120px;
                font-size: 0.9rem;
            }
        }

        /* تحسين النافبار للشاشات الصغيرة */
        @media (max-width: 991.98px) {
            .navbar-nav .nav-link {
                max-width: none;
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
                white-space: nowrap;
            }

            .navbar-nav {
                gap: 0;
            }

            .navbar-collapse {
                background: rgba(255, 255, 255, 0.98);
                border-radius: 8px;
                margin-top: 10px;
                padding: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            }
        }

        /* تحسين إضافي للشاشات الصغيرة جداً */
        @media (max-width: 576px) {
            .navbar-nav .nav-link {
                font-size: 0.85rem;
                padding: 0.4rem 0.6rem;
            }

            .btn {
                font-size: 0.85rem !important;
                padding: 0.4rem 0.8rem !important;
                width: auto !important;
                min-width: 120px;
            }

            .navbar-brand {
                width: auto !important;
            }
        }

        /* تحسين للشاشات الصغيرة جداً */
        @media (max-width: 480px) {
            .navbar-nav .nav-link {
                font-size: 0.8rem;
                padding: 0.3rem 0.5rem;
            }

            .btn {
                font-size: 0.8rem !important;
                padding: 0.3rem 0.6rem !important;
                min-width: 100px;
            }
        }

        /* تحسين زر اللغة */
        .language-switcher-btn {
            white-space: nowrap !important;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
        }

        @media (max-width: 991.98px) {
            .language-switcher-btn {
                max-width: none;
            }
        }

        @media (max-width: 576px) {
            .language-switcher-btn {
                font-size: 0.8rem;
                max-width: 70px;
            }
        }
    </style>

    <!-- Google Analytics - Load after user interaction -->
    <script>
        function loadGA() {
            if (window.gtag) return;
            var script = document.createElement('script');
            script.async = true;
            script.src = 'https://www.googletagmanager.com/gtag/js?id=G-S8WG4K7SER';
            document.head.appendChild(script);

            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-S8WG4K7SER');
            window.gtag = gtag;
        }

        // Load on first user interaction
        ['click', 'scroll', 'keydown', 'mousemove', 'touchstart'].forEach(function(event) {
            document.addEventListener(event, loadGA, {once: true, passive: true});
        });

        // Fallback: load after 2 seconds
        setTimeout(loadGA, 2000);
    </script>

    {{-- Google Tag Manager --}}
    @if($google_tag_manager_id)
    <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','{{ $google_tag_manager_id }}');
    </script>
    @endif

    {{-- Facebook Pixel --}}
    @if($facebook_pixel_id)
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{{ $facebook_pixel_id }}');
        fbq('track', 'PageView');
    </script>
    <noscript>
        <img height="1" width="1" style="display:none"
             src="https://www.facebook.com/tr?id={{ $facebook_pixel_id }}&ev=PageView&noscript=1"/>
    </noscript>
    @endif

    {{-- Custom Head Code --}}
    @if($custom_head_code)
        {!! $custom_head_code !!}
    @endif
</head>

<body>
    {{-- Google Tag Manager (noscript) --}}
    @if($google_tag_manager_id)
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id={{ $google_tag_manager_id }}"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    @endif

    {{-- Custom Body Code --}}
    @if($custom_body_code)
        {!! $custom_body_code !!}
    @endif

    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg j">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center w-50" href="/">
                    @php
                        $currentLocale = app()->getLocale();
                        $logoField = 'logo';

                        // Use language-specific logo if available
                        if ($currentLocale == 'ar' && isset($front) && $front->getFirstMediaUrl('logo_ar')) {
                            $logoField = 'logo_ar';
                        } elseif ($currentLocale == 'en' && isset($front) && $front->getFirstMediaUrl('logo_en')) {
                            $logoField = 'logo_en';
                        }
                    @endphp

                    @if (isset($front) && $front->getFirstMediaUrl($logoField))
                        <img src="{{ $front->getFirstMediaUrl($logoField) }}" alt="Logo" class="me-2"
                            style="max-height: 50px;">
                    @elseif(isset($front) && $front->getFirstMediaUrl('logo'))
                        <img src="{{ $front->getFirstMediaUrl('logo') }}" alt="Logo" class="me-2"
                            style="max-height: 50px;">
                    @else
                        <img src="{{ asset('assets/images/logo.png') }}" alt="Logo" class="me-2"
                            style="max-height: 50px;">
                    @endif
                    {{-- <span>
                        @if (isset($front))
                            {{ $front->hero_title }}
                        @else
                            Digital Identity
                        @endif
                    </span> --}}
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link active text-nowrap" href="{{ url('/') }}"
                                style="color: var(--primary-color); font-weight: bold;">{{ __('Home') }}</a>
                        </li>
                        @if (isset($front) && $front->show_about_section)
                            <li class="nav-item">
                                <a class="nav-link text-nowrap" href="{{ url('/page/about-us') }}"
                                    style="color: var(--secondary-color); transition: color 0.3s;"
                                    onmouseover="this.style.color='var(--primary-color)'"
                                    onmouseout="this.style.color='var(--secondary-color)'">{{ __('About') }}</a>
                            </li>
                        @endif
                        @if (isset($front) && $front->show_services_section)
                            <li class="nav-item">
                                <a class="nav-link text-nowrap" href="{{ url('/#services') }}"
                                    style="color: var(--secondary-color); transition: color 0.3s;"
                                    onmouseover="this.style.color='var(--primary-color)'"
                                    onmouseout="this.style.color='var(--secondary-color)'">{{ __('Services') }}</a>
                            </li>
                        @endif
                        @if (isset($front) && $front->show_pricing_section)
                            <li class="nav-item">
                                <a class="nav-link text-nowrap" href="{{ url('/#pricing') }}"
                                    style="color: var(--secondary-color); transition: color 0.3s;"
                                    onmouseover="this.style.color='var(--primary-color)'"
                                    onmouseout="this.style.color='var(--secondary-color)'">{{ __('Pricing') }}</a>
                            </li>
                        @endif
                        @if (isset($front) && $front->show_faq_section)
                            <li class="nav-item">
                                <a class="nav-link text-nowrap" href="{{ url('/#faq') }}"
                                    style="color: var(--secondary-color); transition: color 0.3s;"
                                    onmouseover="this.style.color='var(--primary-color)'"
                                    onmouseout="this.style.color='var(--secondary-color)'">{{ __('FAQ') }}</a>
                            </li>
                        @endif
                        @if (isset($front) && $front->show_contact_section)
                            <li class="nav-item">
                                <a class="nav-link text-nowrap" href="{{ url('/#contact') }}"
                                    style="color: var(--secondary-color); transition: color 0.3s;"
                                    onmouseover="this.style.color='var(--primary-color)'"
                                    onmouseout="this.style.color='var(--secondary-color)'">{{ __('Contact') }}</a>
                            </li>
                        @endif
                        <!-- Simple Language Switcher Button -->
                        <li class="nav-item">
                            @php
                                $currentLocale = app()->getLocale();
                                $languages = \App\Models\Language::where('status', 1)->get();
                                $currentLanguage = $languages->where('short', $currentLocale)->first();

                                // Get next language for toggle
                                $nextLanguage = null;
                                if ($languages->count() > 1) {
                                    $currentIndex = $languages->search(function ($lang) use ($currentLocale) {
                                        return $lang->short === $currentLocale;
                                    });
                                    $nextIndex = ($currentIndex + 1) % $languages->count();
                                    $nextLanguage = $languages->values()[$nextIndex];
                                }

                                // Language flags mapping
                                $languageFlags = [
                                    'en' => '🇺🇸',
                                    'ar' => '🇸🇦',
                                    'fr' => '🇫🇷',
                                    'es' => '🇪🇸',
                                    'de' => '🇩🇪',
                                    'it' => '🇮🇹',
                                    'pt' => '🇵🇹',
                                    'ru' => '🇷🇺',
                                    'ja' => '🇯🇵',
                                    'ko' => '🇰🇷',
                                    'zh' => '🇨🇳',
                                ];

                                $currentFlag = $languageFlags[$currentLocale] ?? '🌐';
                                $nextFlag = $nextLanguage ? $languageFlags[$nextLanguage->short] ?? '🌐' : '🌐';
                            @endphp

                            @if ($nextLanguage)
                                <a href="{{ route('language.switch', $nextLanguage->short) }}"
                                    class="language-switcher-btn text-nowrap"
                                    title="{{ __('Switch to') }} {{ $nextLanguage->name }}"
                                    data-current="{{ $currentLocale }}" data-next="{{ $nextLanguage->short }}"
                                    style="white-space: nowrap;">
                                    {{-- <div class="language-display">
                                        <span class="current-flag">{{ $currentFlag }}</span>
                                        <i class="fas fa-exchange-alt switch-icon"></i>
                                        <span class="next-flag">{{ $nextFlag }}</span>
                                    </div> --}}
                                    <span class="switch-text">{{ strtoupper($currentLocale) }} →
                                        {{ strtoupper($nextLanguage->short) }}</span>
                                </a>
                            @endif
                        </li>

                        @auth
                            <!-- User is logged in - Show "My Account" button -->
                            <li class="nav-item ms-3">
                                <a href="{{ route('dashboard') }}" class="btn btn-primary text-nowrap"
                                    style="min-width: 120px; padding: 0.5rem 1rem; border-radius: 5px; white-space: nowrap;">
                                    <i class="fas fa-user me-2"></i>{{ __('My Account') }}
                                </a>
                            </li>
                        @else
                            <!-- User is not logged in - Show Login and Get Started buttons -->
                            <li class="nav-item ms-2">
                                <a href="{{ route('login') }}" class="btn btn-outline-primary text-nowrap"
                                    style="min-width: 100px; padding: 0.5rem 1rem; border-radius: 5px; white-space: nowrap;">
                                    <i class="fas fa-sign-in-alt me-2"></i>{{ __('Login') }}
                                </a>
                            </li>
                            @if (isset($front) && $front->hero_button_link)
                                <li class="nav-item ms-2">
                                    <a href="{{ $front->hero_button_link }}" class="btn btn-primary text-nowrap"
                                        style="min-width: 120px; padding: 0.5rem 1rem; border-radius: 5px; white-space: nowrap;">
                                        @if (isset($front) && $front->hero_button_text)
                                            {{ $front->hero_button_text }}
                                        @else
                                            {{ __('Get Started') }}
                                        @endif
                                    </a>
                                </li>
                            @endif
                        @endauth
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    {{ $slot }}

    <!-- Modern Footer Section -->
    <footer class="modern-footer">
        <!-- Footer Background -->
        <div class="footer-bg">
            <div class="footer-gradient"></div>
            <div class="footer-pattern"></div>
            <div class="footer-shapes">
                <div class="footer-shape footer-shape-1"></div>
                <div class="footer-shape footer-shape-2"></div>
                <div class="footer-shape footer-shape-3"></div>
            </div>
        </div>

        <div class="container position-relative">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget" data-aos="fade-up" data-aos-delay="100"
                        data-lang="{{ app()->getLocale() }}">
                        <!-- Logo Section -->
                        <div class="footer-logo mb-4">
                            @php
                                $currentLocale = app()->getLocale();
                                $logoField = 'logo';
                                if ($currentLocale == 'ar' && isset($front) && $front->getFirstMediaUrl('logo_ar')) {
                                    $logoField = 'logo_ar';
                                } elseif (
                                    $currentLocale == 'en' &&
                                    isset($front) &&
                                    $front->getFirstMediaUrl('logo_en')
                                ) {
                                    $logoField = 'logo_en';
                                }
                            @endphp

                            @if (isset($front) && $front->getFirstMediaUrl($logoField))
                                <img src="{{ $front->getFirstMediaUrl($logoField) }}" alt="{{ __('Logo') }}"
                                    class="footer-logo-img">
                            @else
                                <h3 class="footer-brand-text">{{ __('Your Brand') }}</h3>
                            @endif
                        </div>

                        <p class="footer-description">
                            @if (isset($front) && $front->about_description)
                                {{ Str::limit($front->about_description, 150) }}
                            @else
                                {{ __('We provide a comprehensive suite of tools to help you create a professional digital identity. From Link Tree pages to premium profiles, product landing pages, and e-commerce stores.') }}
                            @endif
                        </p>

                        <!-- Social Media Icons -->
                        <div class="footer-social mt-4">
                            @if (isset($front) && isset($front->socials) && is_array($front->socials))
                                @foreach ($front->socials as $social)
                                    <a href="{{ $social['link'] ?? '#' }}" class="footer-social-icon"
                                        target="_blank">
                                        <i class="{{ $social['icon'] ?? 'fab fa-facebook' }}"></i>
                                        <span class="social-ripple"></span>
                                    </a>
                                @endforeach
                            @else
                                <a href="https://www.facebook.com/fanzly.net" class="footer-social-icon">
                                    <i class="fab fa-facebook-f"></i>
                                    <span class="social-ripple"></span>
                                </a>
                                <a href="https://x.com/fanzly_net" class="footer-social-icon">
                                    <i class="fab fa-twitter"></i>
                                    <span class="social-ripple"></span>
                                </a>
                                <a href="https://www.instagram.com/fanzly_net" class="footer-social-icon">
                                    <i class="fab fa-instagram"></i>
                                    <span class="social-ripple"></span>
                                </a>
                                <a href="https://www.linkedin.com/in/fanzly" class="footer-social-icon">
                                    <i class="fab fa-linkedin-in"></i>
                                    <span class="social-ripple"></span>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget" data-aos="fade-up" data-aos-delay="200"
                        data-lang="{{ app()->getLocale() }}">
                        <h4 class="footer-title">{{ __('Quick Links') }}</h4>
                        <ul class="footer-links">
                            <li><a href="{{ url('/') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Home') }}
                                </a></li>
                            <li><a href="{{ url('/page/about-us') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('About Us') }}
                                </a></li>
                            <li><a href="{{ url('/#services') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Services') }}
                                </a></li>
                            <li><a href="{{ url('/#pricing') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Pricing') }}
                                </a></li>
                            <li><a href="{{ url('/#contact') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Contact') }}
                                </a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget" data-aos="fade-up" data-aos-delay="300"
                        data-lang="{{ app()->getLocale() }}">
                        <h4 class="footer-title">{{ __('Our Services') }}</h4>
                        <ul class="footer-links">
                            <li><a href="{{ url('/page/link-tree-service') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Link Tree') }}
                                </a></li>
                            <li><a href="{{ url('/page/premium-profiles-service') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Premium Profiles') }}
                                </a></li>
                            <li><a href="{{ url('/page/digital-business-cards-service') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Business Profiles') }}
                                </a></li>
                            <li><a href="{{ url('/page/e-commerce-store-service') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('E-commerce Stores') }}
                                </a></li>
                            <li><a href="{{ url('/page/developer-tools') }}" class="footer-link">
                                    <i class="fas fa-chevron-right"></i>
                                    {{ __('Developer Tools') }}
                                </a></li>
                        </ul>
                    </div>
                </div>


            </div>

            <!-- Newsletter Section -->
            {{-- <div class="footer-newsletter" data-aos="fade-up" data-aos-delay="500" data-lang="{{ app()->getLocale() }}">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="newsletter-content">
                            <h3 class="newsletter-title">{{ __('Stay Updated') }}</h3>
                            <p class="newsletter-subtitle">{{ __('Subscribe to our newsletter for the latest updates and exclusive offers') }}</p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="newsletter-form">
                            <div class="input-group">
                                <input type="email" class="form-control newsletter-input" placeholder="{{ __('Enter your email') }}">
                                <button class="btn newsletter-btn" type="button">
                                    <i class="fas fa-paper-plane"></i>
                                    {{ __('Subscribe') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div> --}}
        </div>
    </footer>

    <!-- Enhanced Copyright Section -->
    <div class="footer-bottom" data-lang="{{ app()->getLocale() }}">
        <!-- Copyright Background Effects -->
        <div class="copyright-bg">
            <div class="copyright-gradient"></div>
            <div class="copyright-particles">
                <div class="particle particle-1"></div>
                <div class="particle particle-2"></div>
                <div class="particle particle-3"></div>
                <div class="particle particle-4"></div>
                <div class="particle particle-5"></div>
            </div>
        </div>

        <div class="container">
            <div class="footer-bottom-content">
                <div class="row align-items-center">
                    <div class="col-md-6 text-center text-md-start">
                        <div class="copyright-section">
                            <div class="copyright-icon">
                                <i class="fas fa-copyright"></i>
                            </div>
                            <div class="copyright-info">
                                <p class="copyright-text">
                                    {{ date('Y') }} {{ __('All Rights Reserved') }}
                                </p>
                                <p class="copyright-subtitle">
                                    {{ __('Built with modern technology') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        <div class="designed-section">
                            <p class="designed-text">
                                {{ __('Designed with') }}
                                <span class="heart-container">
                                    <i class="fas fa-heart heart-icon"></i>
                                    <i class="fas fa-heart heart-icon-shadow"></i>
                                </span>
                                {{ __('by') }}
                                <a href="https://3bdulrahman.com" target="_blank" class="company-link">
                                    <span class="company-name">{{ __('AbdulRahman') }}</span>
                                    <i class="fas fa-external-link-alt company-icon"></i>
                                </a>
                            </p>
                            <div class="social-badges">
                                <span class="badge-item">
                                    <i class="fas fa-code"></i>
                                    {{ __('Clean Code') }}
                                </span>
                                <span class="badge-item">
                                    <i class="fas fa-mobile-alt"></i>
                                    {{ __('Responsive') }}
                                </span>
                                <span class="badge-item">
                                    <i class="fas fa-rocket"></i>
                                    {{ __('Fast') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <a href="#hero" class="back-to-top position-fixed bottom-0 end-0 m-4"
        style="width: 45px; height: 45px; line-height: 45px; background-color: var(--primary-color); color: white; border-radius: 50%; text-align: center; display: none; z-index: 999; transition: var(--transition);">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- Critical Scripts only -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" defer></script>

    <!-- Load other scripts asynchronously -->
    <script>
        function loadScript(src, callback) {
            var script = document.createElement('script');
            script.src = src;
            script.async = true;
            if (callback) script.onload = callback;
            document.head.appendChild(script);
        }

        window.addEventListener('load', function(){
            loadScript('https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js', function(){
                if (typeof AOS !== 'undefined') {
                    AOS.init({
                        duration: 600,
                        easing: 'ease-out-cubic',
                        once: true,
                        mirror: false,
                        offset: 50,
                        disable: window.innerWidth < 768
                    });
                }
            });

            loadScript('https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js');
        });
    </script>
    @livewireScripts

    <script>
        // Optimized scroll handler with throttling
        let ticking = false;
        function updateOnScroll() {
            const scrollY = window.pageYOffset;

            // Header effects
            const header = document.querySelector('.header');
            if (header) {
                header.classList.toggle('sticky', scrollY > 100);
            }

            // Back to top button
            const backToTop = document.querySelector('.back-to-top');
            if (backToTop) {
                backToTop.style.display = scrollY > 300 ? 'block' : 'none';
            }

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateOnScroll);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick, { passive: true });

        // Modern About Section Animations
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, observerOptions);

            // Observe elements with animation classes
            document.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right').forEach(el => {
                observer.observe(el);
            });

            // Parallax effect for floating elements
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const parallaxElements = document.querySelectorAll('.about-bg-decoration');

                parallaxElements.forEach((element, index) => {
                    const speed = 0.5 + (index * 0.2);
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Hover effects for floating cards
            const floatingCards = document.querySelectorAll('.about-floating-card');
            floatingCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Counter animation for stats
            const animateCounters = () => {
                const counters = document.querySelectorAll('.stat-number');
                counters.forEach(counter => {
                    const target = parseInt(counter.textContent);
                    const increment = target / 100;
                    let current = 0;

                    const updateCounter = () => {
                        if (current < target) {
                            current += increment;
                            counter.textContent = Math.ceil(current);
                            requestAnimationFrame(updateCounter);
                        } else {
                            counter.textContent = target;
                        }
                    };

                    updateCounter();
                });
            };

            // Trigger counter animation when stats card is visible
            const statsCard = document.querySelector('.stats-card');
            if (statsCard) {
                const statsObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            animateCounters();
                            statsObserver.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.5
                });

                statsObserver.observe(statsCard);
            }

            // Modern Pricing Section Animations
            const pricingObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, observerOptions);

            // Observe pricing elements
            document.querySelectorAll('.fade-in-up-pricing').forEach(el => {
                pricingObserver.observe(el);
            });

            // Pricing cards hover effects
            const pricingCards = document.querySelectorAll('.modern-pricing-card');
            pricingCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    // Add glow effect to featured cards
                    if (this.classList.contains('featured')) {
                        this.style.boxShadow = '0 30px 60px rgba(255, 255, 255, 0.3)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (this.classList.contains('featured')) {
                        this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';
                    }
                });
            });

            // Parallax effect for pricing background decorations
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const pricingDecorations = document.querySelectorAll('.pricing-bg-decoration');

                pricingDecorations.forEach((element, index) => {
                    const speed = 0.3 + (index * 0.1);
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Modern Features Section Animations
            const featuresObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, observerOptions);

            // Observe features elements
            document.querySelectorAll('.fade-in-up-features').forEach(el => {
                featuresObserver.observe(el);
            });

            // Features cards hover effects
            const featureCards = document.querySelectorAll('.modern-feature-card');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    // Add special effects for featured cards
                    if (this.classList.contains('featured')) {
                        this.style.boxShadow = '0 25px 50px rgba(102, 126, 234, 0.3)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (this.classList.contains('featured')) {
                        this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.08)';
                    }
                });
            });

            // Parallax effect for features background decorations
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const featuresDecorations = document.querySelectorAll('.features-bg-decoration');

                featuresDecorations.forEach((element, index) => {
                    const speed = 0.2 + (index * 0.1);
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Modern Testimonials Section Animations
            const testimonialsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, observerOptions);

            // Observe testimonials elements
            document.querySelectorAll('.fade-in-up-testimonials').forEach(el => {
                testimonialsObserver.observe(el);
            });

            // Testimonials cards hover effects
            const testimonialCards = document.querySelectorAll('.modern-testimonial-card');
            testimonialCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                    this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.2)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';
                });
            });

            // Testimonials navigation with smooth animations
            let currentTestimonial = 0;
            let isAnimating = false;
            const testimonialSlides = document.querySelectorAll('.testimonials-grid > div');
            const testimonialIndicators = document.querySelectorAll('.testimonial-indicator');
            const prevBtn = document.getElementById('prevTestimonial');
            const nextBtn = document.getElementById('nextTestimonial');

            function showTestimonial(index, direction = 'next') {
                if (isAnimating || index === currentTestimonial) return;

                isAnimating = true;

                // Remove all classes from all slides
                testimonialSlides.forEach((slide, i) => {
                    slide.classList.remove('active', 'prev', 'next');

                    if (i === index) {
                        slide.classList.add('active');
                    } else if (i < index) {
                        slide.classList.add('prev');
                    } else {
                        slide.classList.add('next');
                    }
                });

                // Update indicators
                testimonialIndicators.forEach((indicator, i) => {
                    indicator.classList.toggle('active', i === index);
                });

                // Update current index
                currentTestimonial = index;

                // Reset animation flag after transition
                setTimeout(() => {
                    isAnimating = false;
                }, 600);
            }

            // Navigation event listeners
            if (prevBtn && nextBtn) {
                prevBtn.addEventListener('click', () => {
                    if (isAnimating) return;
                    const newIndex = currentTestimonial > 0 ? currentTestimonial - 1 : testimonialSlides
                        .length - 1;
                    showTestimonial(newIndex, 'prev');
                });

                nextBtn.addEventListener('click', () => {
                    if (isAnimating) return;
                    const newIndex = currentTestimonial < testimonialSlides.length - 1 ?
                        currentTestimonial + 1 : 0;
                    showTestimonial(newIndex, 'next');
                });
            }

            // Indicator event listeners
            testimonialIndicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    if (isAnimating) return;
                    const direction = index > currentTestimonial ? 'next' : 'prev';
                    showTestimonial(index, direction);
                });
            });

            // Auto-play testimonials
            let autoPlayInterval = setInterval(() => {
                if (!isAnimating && nextBtn) {
                    nextBtn.click();
                }
            }, 5000);

            // Pause auto-play on hover
            const testimonialsSection = document.querySelector('.modern-testimonials-section');
            if (testimonialsSection) {
                testimonialsSection.addEventListener('mouseenter', () => {
                    clearInterval(autoPlayInterval);
                });

                testimonialsSection.addEventListener('mouseleave', () => {
                    autoPlayInterval = setInterval(() => {
                        if (!isAnimating && nextBtn) {
                            nextBtn.click();
                        }
                    }, 5000);
                });
            }

            // Touch/Swipe support for mobile
            let touchStartX = 0;
            let touchEndX = 0;

            if (testimonialsSection) {
                testimonialsSection.addEventListener('touchstart', (e) => {
                    touchStartX = e.changedTouches[0].screenX;
                });

                testimonialsSection.addEventListener('touchend', (e) => {
                    touchEndX = e.changedTouches[0].screenX;
                    handleSwipe();
                });
            }

            function handleSwipe() {
                if (isAnimating) return;

                const swipeThreshold = 50;
                const swipeDistance = touchEndX - touchStartX;

                if (Math.abs(swipeDistance) > swipeThreshold) {
                    if (swipeDistance > 0) {
                        // Swipe right - go to previous
                        if (prevBtn) prevBtn.click();
                    } else {
                        // Swipe left - go to next
                        if (nextBtn) nextBtn.click();
                    }
                }
            }

            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (isAnimating) return;

                if (e.key === 'ArrowLeft' && prevBtn) {
                    prevBtn.click();
                } else if (e.key === 'ArrowRight' && nextBtn) {
                    nextBtn.click();
                }
            });

            // Initialize testimonials
            if (testimonialSlides.length > 0) {
                // Set initial state
                testimonialSlides.forEach((slide, i) => {
                    if (i === 0) {
                        slide.classList.add('active');
                    } else {
                        slide.classList.add('next');
                    }
                });

                // Set first indicator as active
                if (testimonialIndicators.length > 0) {
                    testimonialIndicators[0].classList.add('active');
                }
            }

            // Parallax effect for testimonials background decorations
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const testimonialsDecorations = document.querySelectorAll('.testimonials-bg-decoration');

                testimonialsDecorations.forEach((element, index) => {
                    const speed = 0.3 + (index * 0.1);
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Modern FAQ Section Animations
            const faqObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, observerOptions);

            // Observe FAQ elements
            document.querySelectorAll('.fade-in-up-faq').forEach(el => {
                faqObserver.observe(el);
            });

            // Parallax effect for FAQ background decorations
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const faqDecorations = document.querySelectorAll('.faq-bg-decoration');

                faqDecorations.forEach((element, index) => {
                    const speed = 0.2 + (index * 0.1);
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });
        });

        // FAQ Toggle Function
        function toggleFaq(index) {
            const faqItems = document.querySelectorAll('.modern-faq-item');
            const currentItem = faqItems[index];
            const isActive = currentItem.classList.contains('active');

            // Close all FAQ items
            faqItems.forEach(item => {
                item.classList.remove('active');
            });

            // If the clicked item wasn't active, open it
            if (!isActive) {
                currentItem.classList.add('active');
            }
        }

        // Modern CTA Section Animations
        const ctaObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        // Observe CTA elements
        document.querySelectorAll('.fade-in-up-cta').forEach(el => {
            ctaObserver.observe(el);
        });

        // CTA Stats Counter Animation
        const animateCtaCounters = () => {
            const counters = document.querySelectorAll('.cta-stat-number');
            counters.forEach(counter => {
                const text = counter.textContent;
                const target = parseInt(text.replace(/[^\d]/g, ''));
                const suffix = text.replace(/[\d]/g, '');
                const increment = target / 100;
                let current = 0;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current) + suffix;
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = text;
                    }
                };

                updateCounter();
            });
        };

        // Trigger CTA counter animation when visible
        const ctaSection = document.querySelector('.modern-cta-section');
        if (ctaSection) {
            const ctaStatsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCtaCounters();
                        ctaStatsObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.5
            });

            ctaStatsObserver.observe(ctaSection);
        }

        // Parallax effect for CTA background decorations
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const ctaDecorations = document.querySelectorAll('.cta-bg-decoration');

            ctaDecorations.forEach((element, index) => {
                const speed = 0.3 + (index * 0.1);
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            // Modern Contact Section Animations
            const contactObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, observerOptions);

            // Observe contact elements
            document.querySelectorAll('.fade-in-up-contact').forEach(el => {
                contactObserver.observe(el);
            });

            // Contact form enhancements
            const contactInputs = document.querySelectorAll('.modern-form-control');
            contactInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });

            // Parallax effect for contact background decorations
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const contactDecorations = document.querySelectorAll('.contact-bg-decoration');

                contactDecorations.forEach((element, index) => {
                    const speed = 0.2 + (index * 0.1);
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });
        });
    </script>
    @if ($languageDir == 'rtl')
        <style>
            .footer-links {
                direction: ltr;
            }
        </style>
    @endif
    <!-- Modern Footer Styles -->
    <style>
        /* Modern Footer Styles */
        .modern-footer {
            position: relative;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            padding: 80px 0 0;
            overflow: hidden;
        }

        .footer-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .footer-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg,
                    rgba(102, 126, 234, 0.1) 0%,
                    rgba(118, 75, 162, 0.1) 50%,
                    rgba(255, 107, 107, 0.1) 100%);
            animation: gradientShift 8s ease-in-out infinite;
        }

        .footer-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: patternMove 20s linear infinite;
        }

        .footer-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .footer-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
            animation: float 6s ease-in-out infinite;
        }

        .footer-shape-1 {
            width: 120px;
            height: 120px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .footer-shape-2 {
            width: 80px;
            height: 80px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .footer-shape-3 {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 70%;
            animation-delay: 4s;
        }

        @keyframes gradientShift {

            0%,
            100% {
                transform: translateX(-100%);
            }

            50% {
                transform: translateX(100%);
            }
        }

        @keyframes patternMove {
            0% {
                transform: translate(0, 0);
            }

            100% {
                transform: translate(50px, 50px);
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .footer-widget {
            position: relative;
            z-index: 2;
            margin-bottom: 40px;
        }

        /* Footer Logo */
        .footer-logo-img {
            max-height: 50px;
            width: auto;
            filter: brightness(1.2);
        }

        .footer-brand-text {
            color: #fff;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* Footer Titles */
        .footer-title {
            color: #fff;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 25px;
            position: relative;
            padding-left: 20px;
        }

        .footer-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        /* Footer Links */
        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 12px;
        }

        .footer-link {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            padding: 5px 0;
        }

        .footer-link i {
            font-size: 10px;
            margin-right: 10px;
            transition: all 0.3s ease;
            opacity: 0.6;
        }

        .footer-link:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .footer-link:hover i {
            opacity: 1;
            transform: translateX(3px);
        }

        /* Footer Social Icons */
        .footer-social {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .footer-social-icon {
            position: relative;
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .footer-social-icon:hover {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: transparent;
            color: #fff;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .social-ripple {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .footer-social-icon:hover .social-ripple {
            width: 100%;
            height: 100%;
        }



        /* Newsletter Section */
        .footer-newsletter {
            margin-top: 60px;
            padding: 40px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .newsletter-content {
            margin-bottom: 20px;
        }

        .newsletter-title {
            color: #fff;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .newsletter-subtitle {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }

        .newsletter-form .input-group {
            max-width: 400px;
            margin-left: auto;
        }

        .newsletter-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 12px 20px;
            border-radius: 25px 0 0 25px;
            backdrop-filter: blur(10px);
        }

        .newsletter-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .newsletter-input:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            color: #fff;
        }

        .newsletter-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: #fff;
            padding: 12px 25px;
            border-radius: 0 25px 25px 0;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .newsletter-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: #fff;
        }

        /* Enhanced Footer Bottom */
        .footer-bottom {
            position: relative;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%);
            padding: 40px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        /* Copyright Background Effects */
        .copyright-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .copyright-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg,
                    rgba(102, 126, 234, 0.05) 0%,
                    rgba(118, 75, 162, 0.05) 50%,
                    rgba(255, 107, 107, 0.05) 100%);
            animation: gradientFlow 10s ease-in-out infinite;
        }

        .copyright-particles {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: particleFloat 8s linear infinite;
        }

        .particle-1 {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .particle-2 {
            top: 60%;
            left: 30%;
            animation-delay: 2s;
        }

        .particle-3 {
            top: 40%;
            right: 20%;
            animation-delay: 4s;
        }

        .particle-4 {
            bottom: 30%;
            left: 60%;
            animation-delay: 6s;
        }

        .particle-5 {
            top: 80%;
            right: 40%;
            animation-delay: 1s;
        }

        @keyframes gradientFlow {

            0%,
            100% {
                transform: translateX(-100%);
                opacity: 0.3;
            }

            50% {
                transform: translateX(100%);
                opacity: 0.6;
            }
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .footer-bottom-content {
            position: relative;
            z-index: 2;
        }

        /* Copyright Section */
        .copyright-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .copyright-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .copyright-icon i {
            color: #fff;
            font-size: 16px;
        }

        .copyright-info {
            flex: 1;
        }

        .copyright-text {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.2;
        }

        .copyright-subtitle {
            color: rgba(255, 255, 255, 0.6);
            margin: 5px 0 0 0;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Designed Section */
        .designed-section {
            text-align: right;
        }

        .designed-text {
            color: rgba(255, 255, 255, 0.8);
            margin: 0 0 15px 0;
            font-size: 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 8px;
        }

        /* Heart Animation */
        .heart-container {
            position: relative;
            display: inline-block;
            margin: 0 5px;
        }

        .heart-icon {
            color: #ff6b6b;
            font-size: 16px;
            animation: heartbeat 1.5s ease-in-out infinite;
        }

        .heart-icon-shadow {
            position: absolute;
            top: 0;
            left: 0;
            color: #ff6b6b;
            font-size: 16px;
            opacity: 0.3;
            animation: heartPulse 1.5s ease-in-out infinite;
        }

        @keyframes heartbeat {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.2);
            }
        }

        @keyframes heartPulse {

            0%,
            100% {
                transform: scale(1);
                opacity: 0.3;
            }

            50% {
                transform: scale(1.4);
                opacity: 0.1;
            }
        }

        /* Company Link */
        .company-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .company-link:hover {
            color: #fff;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .company-name {
            font-weight: 700;
        }

        .company-icon {
            font-size: 12px;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .company-link:hover .company-icon {
            opacity: 1;
            transform: translateX(2px);
        }

        /* Social Badges */
        .social-badges {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        .badge-item {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 5px 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .badge-item:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.4);
            color: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
        }

        .badge-item i {
            font-size: 10px;
            opacity: 0.8;
        }

        /* RTL Support for Copyright */
        [dir="rtl"] .copyright-section {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .designed-section {
            text-align: left;
        }

        [dir="rtl"] .designed-text {
            justify-content: flex-start;
        }

        [dir="rtl"] .social-badges {
            justify-content: flex-start;
        }

        [dir="rtl"] .company-link:hover .company-icon {
            transform: translateX(-2px);
        }

        /* Language-specific adjustments for Copyright */
        .footer-bottom[data-lang="ar"] .copyright-text,
        .footer-bottom[data-lang="ar"] .copyright-subtitle,
        .footer-bottom[data-lang="ar"] .designed-text,
        .footer-bottom[data-lang="ar"] .company-name,
        .footer-bottom[data-lang="ar"] .badge-item {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .footer-bottom[data-lang="ar"] .copyright-text {
            font-weight: 700;
            font-size: 15px;
            line-height: 1.4;
        }

        .footer-bottom[data-lang="ar"] .copyright-subtitle {
            font-weight: 500;
            font-size: 11px;
            letter-spacing: 0;
            line-height: 1.3;
        }

        .footer-bottom[data-lang="ar"] .designed-text {
            font-weight: 600;
            font-size: 14px;
            line-height: 1.4;
        }

        .footer-bottom[data-lang="ar"] .badge-item {
            font-weight: 600;
            letter-spacing: 0;
            font-size: 10px;
            padding: 6px 10px;
        }

        .footer-bottom[data-lang="ar"] .company-name {
            font-weight: 700;
        }

        /* RTL specific improvements */
        [dir="rtl"] .footer-bottom-content .row {
            direction: rtl;
        }

        [dir="rtl"] .footer-bottom-content .col-md-6:first-child {
            text-align: right;
        }

        [dir="rtl"] .footer-bottom-content .col-md-6:last-child {
            text-align: left;
        }

        [dir="rtl"] .copyright-section {
            flex-direction: row;
            text-align: right;
            justify-content: flex-start;
        }

        [dir="rtl"] .copyright-info {
            text-align: right;
        }

        [dir="rtl"] .designed-section {
            text-align: left;
        }

        [dir="rtl"] .designed-text {
            justify-content: flex-start;
            flex-direction: row;
            text-align: left;
        }

        [dir="rtl"] .social-badges {
            justify-content: flex-start;
        }

        [dir="rtl"] .company-link {
            flex-direction: row;
        }

        [dir="rtl"] .company-link:hover .company-icon {
            transform: translateX(-2px);
        }

        /* RTL Desktop specific */
        @media (min-width: 769px) {
            [dir="rtl"] .footer-bottom-content .col-md-6:first-child {
                text-align: right;
                padding-right: 15px;
            }

            [dir="rtl"] .footer-bottom-content .col-md-6:last-child {
                text-align: left;
                padding-left: 15px;
            }

            [dir="rtl"] .copyright-section {
                justify-content: flex-start;
            }

            [dir="rtl"] .designed-text {
                justify-content: flex-start;
            }

            [dir="rtl"] .social-badges {
                justify-content: flex-start;
            }
        }

        /* Better spacing for Arabic text */
        .footer-bottom[data-lang="ar"] .copyright-section {
            gap: 12px;
        }

        .footer-bottom[data-lang="ar"] .designed-text {
            gap: 6px;
            margin-bottom: 12px;
        }

        .footer-bottom[data-lang="ar"] .social-badges {
            gap: 8px;
        }

        .footer-bottom[data-lang="ar"] .heart-container {
            margin: 0 3px;
        }

        /* RTL Arabic specific adjustments */
        [dir="rtl"] .footer-bottom[data-lang="ar"] .copyright-section {
            text-align: right;
            justify-content: flex-start;
        }

        [dir="rtl"] .footer-bottom[data-lang="ar"] .copyright-info {
            text-align: right;
        }

        [dir="rtl"] .footer-bottom[data-lang="ar"] .designed-section {
            text-align: left;
        }

        [dir="rtl"] .footer-bottom[data-lang="ar"] .designed-text {
            text-align: left;
            justify-content: flex-start;
        }

        [dir="rtl"] .footer-bottom[data-lang="ar"] .social-badges {
            justify-content: flex-start;
        }

        /* Enhanced text readability for Arabic */
        .footer-bottom[data-lang="ar"] .copyright-text,
        .footer-bottom[data-lang="ar"] .designed-text {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .footer-bottom[data-lang="ar"] .badge-item {
            border-radius: 15px;
        }

        .footer-bottom[data-lang="ar"] .company-link {
            border-radius: 12px;
        }

        /* Improved contrast for Arabic text */
        .footer-bottom[data-lang="ar"] .copyright-text {
            color: rgba(255, 255, 255, 0.95);
        }

        .footer-bottom[data-lang="ar"] .designed-text {
            color: rgba(255, 255, 255, 0.85);
        }

        .footer-bottom[data-lang="ar"] .copyright-subtitle {
            color: rgba(255, 255, 255, 0.65);
        }

        /* RTL Support */
        [dir="rtl"] .footer-title {
            padding-right: 20px;
            padding-left: 0;
        }

        [dir="rtl"] .footer-title::before {
            right: 0;
            left: auto;
        }

        [dir="rtl"] .footer-link {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .footer-link i {
            margin-left: 10px;
            margin-right: 0;
            transform: rotate(180deg);
        }

        [dir="rtl"] .footer-link:hover {
            transform: translateX(-5px);
        }

        [dir="rtl"] .footer-link:hover i {
            transform: translateX(-3px) rotate(180deg);
        }

        [dir="rtl"] .contact-icon {
            margin-left: 15px;
            margin-right: 0;
        }

        [dir="rtl"] .newsletter-form .input-group {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .newsletter-input {
            border-radius: 0 25px 25px 0;
            text-align: right;
        }

        [dir="rtl"] .newsletter-btn {
            border-radius: 25px 0 0 25px;
        }

        /* Language-specific adjustments */
        .footer-widget[data-lang="ar"] .footer-title,
        .footer-widget[data-lang="ar"] .footer-description,
        .footer-widget[data-lang="ar"] .footer-link,
        .footer-newsletter[data-lang="ar"] .newsletter-title,
        .footer-newsletter[data-lang="ar"] .newsletter-subtitle,
        .footer-newsletter[data-lang="ar"] .newsletter-btn {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .footer-widget[data-lang="ar"] .footer-title,
        .footer-newsletter[data-lang="ar"] .newsletter-title {
            font-weight: 700;
            letter-spacing: 0;
        }

        .footer-widget[data-lang="ar"] .footer-link {
            font-weight: 500;
        }

        .footer-widget[data-lang="ar"] .footer-description,
        .footer-newsletter[data-lang="ar"] .newsletter-subtitle {
            line-height: 1.8;
            font-weight: 400;
        }

        /* Text alignment for RTL */
        [dir="rtl"] .footer-widget {
            text-align: right;
        }

        [dir="rtl"] .footer-social {
            justify-content: flex-end;
        }

        [dir="rtl"] .newsletter-content {
            text-align: right;
        }

        [dir="rtl"] .newsletter-input::placeholder {
            text-align: right;
        }

        /* Better spacing for Arabic text */
        .footer-widget[data-lang="ar"] .footer-links li {
            margin-bottom: 15px;
        }

        .footer-widget[data-lang="ar"] .footer-link {
            padding: 8px 0;
        }

        /* Improved button styling for Arabic */
        .footer-newsletter[data-lang="ar"] .newsletter-btn {
            font-weight: 600;
            letter-spacing: 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .modern-footer {
                padding: 60px 0 0;
            }

            .footer-widget {
                margin-bottom: 30px;
                text-align: center;
            }

            .footer-title {
                text-align: center;
                padding-left: 0;
                padding-right: 0;
            }

            .footer-title::before {
                display: none;
            }

            .footer-social {
                justify-content: center;
            }

            .newsletter-form .input-group {
                margin: 0;
                flex-direction: column;
            }

            .newsletter-input,
            .newsletter-btn {
                border-radius: 25px;
                margin-bottom: 10px;
            }

            .newsletter-btn {
                width: 100%;
            }

            /* Copyright Mobile Styles */
            .footer-bottom {
                padding: 30px 0;
            }

            .copyright-section {
                justify-content: center;
                margin-bottom: 20px;
            }

            .designed-section {
                text-align: center;
            }

            .designed-text {
                justify-content: center;
                flex-wrap: wrap;
                margin-bottom: 10px;
            }

            .social-badges {
                justify-content: center;
            }

            .badge-item {
                font-size: 10px;
                padding: 4px 8px;
            }

            /* RTL Mobile adjustments */
            [dir="rtl"] .footer-title {
                padding-right: 0;
                text-align: center;
            }

            [dir="rtl"] .footer-widget {
                text-align: center;
            }

            [dir="rtl"] .footer-social {
                justify-content: center;
            }

            [dir="rtl"] .newsletter-content {
                text-align: center;
            }

            [dir="rtl"] .newsletter-input {
                border-radius: 25px;
                text-align: center;
            }

            [dir="rtl"] .newsletter-btn {
                border-radius: 25px;
            }

            /* Copyright RTL Mobile */
            [dir="rtl"] .footer-bottom-content .col-md-6:first-child,
            [dir="rtl"] .footer-bottom-content .col-md-6:last-child {
                text-align: center;
                padding: 0 15px;
            }

            [dir="rtl"] .copyright-section {
                justify-content: center;
                text-align: center;
                flex-direction: row;
            }

            [dir="rtl"] .copyright-info {
                text-align: center;
            }

            [dir="rtl"] .designed-section {
                text-align: center;
            }

            [dir="rtl"] .designed-text {
                justify-content: center;
                flex-direction: row;
                text-align: center;
            }

            [dir="rtl"] .social-badges {
                justify-content: center;
            }

            /* RTL Arabic Mobile specific */
            [dir="rtl"] .footer-bottom[data-lang="ar"] .copyright-section {
                text-align: center;
                justify-content: center;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .copyright-info {
                text-align: center;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .designed-section {
                text-align: center;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .designed-text {
                text-align: center;
                justify-content: center;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .social-badges {
                justify-content: center;
            }

            /* Mobile font adjustments for Arabic */
            .footer-widget[data-lang="ar"] .footer-title {
                font-size: 1.1rem;
            }

            .footer-widget[data-lang="ar"] .footer-link {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 576px) {
            .footer-shape {
                display: none;
            }

            .newsletter-content {
                text-align: center;
                margin-bottom: 25px;
            }

            /* Small mobile adjustments for Arabic */
            .footer-widget[data-lang="ar"] .footer-title {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .footer-widget[data-lang="ar"] .footer-description {
                font-size: 0.9rem;
                line-height: 1.7;
            }

            .footer-newsletter[data-lang="ar"] .newsletter-title {
                font-size: 1.2rem;
            }

            .footer-newsletter[data-lang="ar"] .newsletter-subtitle {
                font-size: 0.85rem;
            }

            /* Copyright Small Mobile */
            .footer-bottom {
                padding: 25px 0;
            }

            .copyright-section {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .copyright-icon {
                width: 35px;
                height: 35px;
            }

            .copyright-icon i {
                font-size: 14px;
            }

            .copyright-text {
                font-size: 14px;
            }

            .copyright-subtitle {
                font-size: 11px;
            }

            .designed-text {
                font-size: 13px;
                gap: 5px;
            }

            .social-badges {
                gap: 5px;
            }

            .badge-item {
                font-size: 9px;
                padding: 3px 6px;
            }

            .company-link {
                padding: 3px 8px;
                font-size: 13px;
            }

            /* Arabic adjustments for small mobile */
            .footer-bottom[data-lang="ar"] .copyright-text {
                font-size: 13px;
                line-height: 1.3;
            }

            .footer-bottom[data-lang="ar"] .copyright-subtitle {
                font-size: 10px;
                line-height: 1.2;
            }

            .footer-bottom[data-lang="ar"] .designed-text {
                font-size: 12px;
                line-height: 1.3;
                gap: 4px;
            }

            .footer-bottom[data-lang="ar"] .badge-item {
                font-size: 8px;
                padding: 2px 5px;
            }

            .footer-bottom[data-lang="ar"] .company-link {
                font-size: 12px;
                padding: 2px 6px;
            }

            /* RTL Small Mobile */
            [dir="rtl"] .footer-bottom-content .col-md-6:first-child,
            [dir="rtl"] .footer-bottom-content .col-md-6:last-child {
                text-align: center;
                padding: 0 10px;
            }

            [dir="rtl"] .copyright-section {
                flex-direction: column;
                text-align: center;
                justify-content: center;
            }

            [dir="rtl"] .copyright-info {
                text-align: center;
            }

            [dir="rtl"] .designed-section {
                text-align: center;
            }

            [dir="rtl"] .designed-text {
                flex-direction: row;
                justify-content: center;
                text-align: center;
            }

            [dir="rtl"] .social-badges {
                justify-content: center;
            }

            /* RTL Arabic Small Mobile specific */
            [dir="rtl"] .footer-bottom[data-lang="ar"] .copyright-section {
                text-align: center;
                justify-content: center;
                gap: 8px;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .copyright-info {
                text-align: center;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .designed-section {
                text-align: center;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .designed-text {
                text-align: center;
                justify-content: center;
                gap: 3px;
            }

            [dir="rtl"] .footer-bottom[data-lang="ar"] .social-badges {
                justify-content: center;
                gap: 5px;
            }
        }
        }
    </style>
</body>

</html>
