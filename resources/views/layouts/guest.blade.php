<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- SEO Meta Tags from Settings --}}
    <title>{{ $meta_title ?? $site_name ?? config('app.name', 'Laravel') }}</title>
    <meta name="description" content="{{ $meta_description ?? $site_description }}">
    <meta name="keywords" content="{{ $meta_keywords ?? $site_keywords }}">
    <meta name="author" content="{{ $site_name }}">

    {{-- Open Graph Meta Tags --}}
    <meta property="og:title" content="{{ $meta_title ?? $site_name }}">
    <meta property="og:description" content="{{ $meta_description ?? $site_description }}">
    <meta property="og:image" content="{{ $og_image }}">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ $site_name }}">

    {{-- Twitter Card Meta Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $meta_title ?? $site_name }}">
    <meta name="twitter:description" content="{{ $meta_description ?? $site_description }}">
    <meta name="twitter:image" content="{{ $og_image }}">

    {{-- Additional Meta Tags --}}
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ url()->current() }}">

    {{-- Favicon --}}
    <link rel="icon" type="image/x-icon" href="{{ $site_icon }}">
    <link rel="shortcut icon" href="{{ $site_icon }}">
    <link rel="apple-touch-icon" href="{{ $site_icon }}">

    <!-- Styles -->
    <link href="{{ asset('assets/dashboard/v1/plugins/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/plugins/font-awesome/css/all.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/plugins/perfectscroll/perfect-scrollbar.css') }}" rel="stylesheet">
    <!-- Theme Styles -->
    <link href="{{ asset('assets/dashboard/v1/css/main.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/dashboard/v1/css/custom.css') }}" rel="stylesheet">
    <!-- Styles -->
    {{-- <link rel="stylesheet" href="{{ mix('css/app.css') }}"> --}}

    @livewireStyles

    {{-- Google Analytics --}}
    @if($google_analytics_id)
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ $google_analytics_id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ $google_analytics_id }}');
    </script>
    @endif

    {{-- Google Tag Manager --}}
    @if($google_tag_manager_id)
    <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','{{ $google_tag_manager_id }}');
    </script>
    @endif

    {{-- Facebook Pixel --}}
    @if($facebook_pixel_id)
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{{ $facebook_pixel_id }}');
        fbq('track', 'PageView');
    </script>
    <noscript>
        <img height="1" width="1" style="display:none"
             src="https://www.facebook.com/tr?id={{ $facebook_pixel_id }}&ev=PageView&noscript=1"/>
    </noscript>
    @endif

    {{-- Custom Head Code --}}
    @if($custom_head_code)
        {!! $custom_head_code !!}
    @endif

    {{-- <script src="{{ mix('js/app.js') }}" defer></script> --}}
</head>

<body class="login-page">
    {{-- Google Tag Manager (noscript) --}}
    @if($google_tag_manager_id)
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id={{ $google_tag_manager_id }}"
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    @endif

    {{-- Custom Body Code --}}
    @if($custom_body_code)
        {!! $custom_body_code !!}
    @endif

    <div class='loader'>
        <div class='spinner-grow text-primary' role='status'>
            <span class='sr-only'>Loading...</span>
        </div>
    </div>
    <!-- Page Content -->

    {{ $slot }}

    @stack('modals')
    <!-- Scripts -->
    <script src="{{ asset('assets/dashboard/v1/plugins/jquery/jquery-3.4.1.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/popper.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/plugins/bootstrap/js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/feather.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/plugins/perfectscroll/perfect-scrollbar.min.js') }}"></script>
    <script src="{{ asset('assets/dashboard/v1/js/main.min.js') }}"></script>

    @livewireScripts
</body>

</html>
