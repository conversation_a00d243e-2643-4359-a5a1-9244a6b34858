<div>
    <div class="card position-relative shadow-sm">
        <div class="card-header p-3 bg-primary text-white">
            <div class="d-flex justify-content-center">
                <h5 class="mb-0 fw-bold">{{ __('linktree') }}</h5>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12 mb-4">
                    <label class="form-label fw-semibold" for="slug">{{ __('Slug') }} </label>
                    <div class="col-12 d-flex justify-content-center align-items-center" dir="ltr">
                        <div class="input-group">
                            <span class="input-group-text bg-light">{{ url('linktree') }}/</span>
                            <input wire:model.live="slug" id="slug" type="text" class="form-control {{ $isSlugAvailable && strlen($slug) >= 4 ? 'is-valid' : (strlen($slug) > 0 ? 'is-invalid' : '') }}"
                                placeholder="{{ __('Enter your slug') }}" autocomplete="off" />
                        </div>
                    </div>
                    @if($slug)
                        <div class="{{ $slugClass }} feedback mt-2 small">
                            {{ $slugMessage }}
                        </div>
                    @endif
                    @error('slug')
                        <span class="text-danger small mt-1">{{ $message }}</span>
                    @enderror
                </div>
                <div class="col-6 p-4 {{ $languageDir == 'rtl' ? 'border-start' : 'border-end' }} bg-light rounded">
                    <h4 class="mb-3 fw-bold text-primary">{{ __('Create LinkTree') }}</h4>
                    <div class="col col-12 mb-4 row">
                        <div class="col col-6 mb-3">
                            <label class="form-label fw-semibold" for="avatar">{{ __('Avatar') }}</label>
                            <input wire:model="avatar" id="avatar" type="file" class="form-control form-control-sm mt-1 border-secondary-subtle"
                                accept="image/*" />
                            @error('avatar')
                                <span class="text-danger small mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col col-6 mb-3">
                            <label class="form-label fw-semibold" for="cover">{{ __('Cover') }}</label>
                            <input wire:model="cover" id="cover" type="file" class="form-control form-control-sm mt-1 border-secondary-subtle"
                                accept="image/*" />
                            @error('cover')
                                <span class="text-danger small mt-1">{{ $message }}</span>
                            @enderror
                        </div>
                        <ul class="nav nav-tabs nav-fill mb-3" id="languageTabs" role="tablist">
                            @forelse ($availablelanguages as $language)
                                <li class="nav-item {{ $loop->first ? 'active' : '' }}" role="presentation">
                                    <button class="nav-link {{ $loop->first ? 'active' : '' }} fw-medium"
                                        id="{{ $language->short }}-tab" data-bs-toggle="tab"
                                        data-bs-target="#{{ $language->short }}" type="button" role="tab"
                                        aria-controls="{{ $language->short }}"
                                        aria-selected="{{ $loop->first ? 'true' : 'false' }}">
                                        {{ $language->name }}
                                    </button>
                                </li>
                            @empty
                            @endforelse
                        </ul>
                        <div class="tab-content mt-3" id="languageTabsContent">
                            @forelse ($availablelanguages as $language)
                                <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }}"
                                    id="{{ $language->short }}" role="tabpanel"
                                    aria-labelledby="{{ $language->short }}-tab">
                                    <div class="col col-12">
                                        <label class="form-label"
                                            for="name_{{ $language->short }}">{{ __('Name') }}
                                            ({{ $language->name }})
                                        </label>
                                        <input
                                            {{ $loop->first ? 'wire:model.live' : 'wire:model.defer' }}="name.{{ $language->short }}"
                                            id="name_{{ $language->short }}" type="text" class="form-control mt-1"
                                            autocomplete="off" />
                                        @error('name.' . $language->short)
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col col-12 mt-3">
                                        <label class="form-label"
                                            for="job_title_{{ $language->short }}">{{ __('Job Title') }}
                                            ({{ $language->name }})</label>
                                        <input
                                            {{ $loop->first ? 'wire:model.live' : 'wire:model.defer' }}="job_title.{{ $language->short }}"
                                            id="job_title_{{ $language->short }}" type="text"
                                            class="form-control mt-1" />
                                        @error('job_title.' . $language->short)
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col col-12 mt-3">
                                        <label class="form-label"
                                            for="description_{{ $language->short }}">{{ __('Description') }}
                                            ({{ $language->name }})</label>
                                        <textarea {{ $loop->first ? 'wire:model.live' : 'wire:model.defer' }}="description.{{ $language->short }}"
                                            id="description_{{ $language->short }}" class="form-control mt-1"></textarea>
                                        @error('description.' . $language->short)
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col col-12 mt-3">
                                        <label class="form-label"
                                            for="keywords_{{ $language->short }}">{{ __('Keywords') }}
                                            ({{ $language->name }})</label>
                                        <input {{ $loop->first ? 'wire:model.live' : 'wire:model.defer' }}="keywords.{{ $language->short }}"
                                            id="keywords_{{ $language->short }}" type="text" class="form-control mt-1"
                                            placeholder="{{ __('Enter keywords separated by commas') }}" />
                                        <small class="form-text text-muted">{{ __('Example: web developer, Laravel, PHP, programming') }}</small>
                                        @error('keywords.' . $language->short)
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            @empty
                            @endforelse
                        </div>
                    </div>
                    <div class="col col-12 mb-4">
                        <label class="form-label fw-semibold" for="default_language">{{ __('Default Language') }}</label>
                        <select wire:model.live="default_language" id="default_language" type="text"
                            class="form-select form-select-sm mt-1 border-secondary-subtle">
                            @forelse ($availablelanguages as $lang)
                                <option value="{{ $lang->id }}">{{ $lang->name }}</option>
                            @empty
                            @endforelse
                        </select>
                        @error('default_language')
                            <span class="text-danger small mt-1">{{ $message }}</span>
                        @enderror
                    </div>
                    <style>
                        .color_settings {
                            position: fixed;

                            [dir="ltr"] & {
                                left: 0px;
                            }

                            [dir="rtl"] & {
                                right: 0px;
                            }

                            top: 50%;
                            transform: translateY(-50%);
                            z-index: 10500;
                            border-radius: 50%;
                            width: 50px;
                            height: 50px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                            transition: all 0.3s ease;
                        }

                        .color_settings:hover {
                            transform: translateY(-50%) scale(1.1);
                        }

                        .link-item,
                        .social-item {
                            background-color: #f8f9fa;
                            border-radius: 12px;
                            padding: 20px;
                            margin-bottom: 15px;
                            position: relative;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                            border: 1px solid rgba(0, 0, 0, 0.05);
                            transition: all 0.3s ease;
                        }

                        .link-item:hover,
                        .social-item:hover {
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        }

                        .link-item .remove-link,
                        .social-item .remove-social {
                            position: absolute;
                            top: 10px;

                            [dir="rtl"] & {
                                left: 10px;
                            }

                            [dir="ltr"] & {
                                right: 10px;
                            }

                            border-radius: 50%;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0;
                            transition: all 0.2s ease;
                        }

                        .link-item .remove-link:hover,
                        .social-item .remove-social:hover {
                            transform: scale(1.1);
                        }

                        .link-btn {
                            display: block;
                            width: 100%;
                            padding: 12px;
                            margin-bottom: 12px;
                            border-radius: 12px;
                            text-align: center;
                            text-decoration: none;
                            transition: all 0.3s ease;
                            font-weight: 500;
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                        }

                        .link-btn:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                        }

                        .social-icons a {
                            display: inline-flex;
                            justify-content: center;
                            align-items: center;
                            width: 45px;
                            height: 45px;
                            border-radius: 50%;
                            margin: 0 8px;
                            text-decoration: none;
                            transition: all 0.3s ease;
                            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                        }

                        .social-icons a:hover {
                            transform: translateY(-3px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                        }

                        /* Color Panel Fix */
                        .color-panel {
                            width: 320px;
                            transform: {{ $languageDir == 'rtl' ? 'translateX(-100%)' : 'translateX(100%)' }};
                            transition: transform 0.3s ease-in-out;
                            z-index: 1060;
                            box-shadow: 0 0 25px rgba(0, 0, 0, 0.15);
                            border-radius: 0 0 0 15px;
                        }

                        .color-panel.open {
                            transform: translateX(0);
                        }

                        .form-control[type="color"] {
                            height: 40px;
                            cursor: pointer;
                        }
                    </style>
                    <!-- Color Settings Button -->
                    <div class="col col-12 mb-3">
                        <button type="button" class="btn btn-primary color_settings" id="colorSettingsBtn">
                            <i class="fas fa-paint-brush"></i>
                        </button>
                    </div>

                    <!-- Color Settings Panel -->
                    <div id="colorSettings"
                        class="color-panel position-fixed {{ $languageDir == 'rtl' ? 'start-0' : 'end-0' }} top-0 h-100 bg-white shadow-lg p-4"
                        wire:ignore>
                        <!-- Close button -->
                        <button type="button" class="btn btn-light close-color-panel shadow-sm" id="closeColorPanelBtn"
                            style="position: absolute; left: -45px; top: 50%; transform: translateY(-50%);
                            border-radius: 50%; width: 40px; height: 40px; display: flex;
                            align-items: center; justify-content: center;">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <h5 class="mb-4 fw-bold text-primary">{{ __('Color Settings') }}</h5>

                        <div class="mb-3">
                            <label class="form-label fw-semibold" for="title_color">{{ __('Title Color') }}</label>
                            <input wire:model.live="title_color" id="title_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $title_color }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold" for="job_title_color">{{ __('Job Title Color') }}</label>
                            <input wire:model.live="job_title_color" id="job_title_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $job_title_color }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold"
                                for="description_color">{{ __('Description Color') }}</label>
                            <input wire:model.live="description_color" id="description_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $description_color }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold" for="icon_color">{{ __('Icon Color') }}</label>
                            <input wire:model.live="icon_color" id="icon_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $icon_color }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold"
                                for="button_text_color">{{ __('Button Text Color') }}</label>
                            <input wire:model.live="button_text_color" id="button_text_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $button_text_color }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold" for="button_color">{{ __('Button Color') }}</label>
                            <input wire:model.live="button_color" id="button_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $button_color }}</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold"
                                for="background_color">{{ __('Background Color') }}</label>
                            <input wire:model.live="background_color" id="background_color" type="color"
                                class="form-control mt-1 border" />
                            <div class="color-preview mt-1 small text-muted">{{ $background_color }}</div>
                        </div>

                        <!-- Close button at bottom of panel -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary w-100" id="closeColorPanelBtnBottom">
                                {{ __('Close Color Panel') }}
                            </button>
                        </div>
                    </div>
                    <div class="col col-12 mb-4">
                        <label class="form-label fw-semibold" for="is_active">{{ __('Status') }}</label>
                        <div class="form-check form-switch mt-1">
                            <input wire:model.live="is_active" id="is_active" type="checkbox"
                                class="form-check-input" style="width: 3em; height: 1.5em;" />
                        </div>
                        @error('is_active')
                            <span class="text-danger small mt-1">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col col-12 mb-4 mt-4 d-flex justify-content-between">
                        <!-- Social Media Management Section -->
                        <div class="col col-5">
                            <h5 class="mb-3">
                                <button class="btn btn-outline-primary rounded-pill btn-link text-decoration-none" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#socialMediaCollapse"
                                    aria-expanded="false" aria-controls="socialMediaCollapse">
                                    <i class="fas fa-share-alt me-2"></i> {{ __('Manage Social Media') }}
                                </button>
                            </h5>
                            <div class="collapse shadow-sm rounded p-3 border" id="socialMediaCollapse" wire:ignore.self>
                                <div class="social-items-container">
                                    @foreach ($socialMedia ?? [] as $index => $social)
                                        <div class="social-item">
                                            <button type="button" class="btn btn-sm btn-danger remove-social"
                                                wire:click="removeSocialMedia({{ $index }})">
                                                <i class="fas fa-times"></i>
                                            </button>

                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">{{ __('Platform') }}</label>
                                                <select class="form-select form-select-sm"
                                                    wire:model.live="socialMedia.{{ $index }}.platform"
                                                    wire:change="updateSocialMediaUrl({{ $index }})">
                                                    <option value="facebook">Facebook</option>
                                                    <option value="twitter">Twitter</option>
                                                    <option value="instagram">Instagram</option>
                                                    <option value="linkedin">LinkedIn</option>
                                                    <option value="youtube">YouTube</option>
                                                    <option value="github">GitHub</option>
                                                    <option value="dribbble">Dribbble</option>
                                                    <option value="behance">Behance</option>
                                                    <option value="pinterest">Pinterest</option>
                                                    <option value="snapchat">Snapchat</option>
                                                    <option value="telegram">Telegram</option>
                                                    <option value="whatsapp">WhatsApp</option>
                                                    <option value="discord">Discord</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">{{ __('URL') }}</label>
                                                <input type="url" class="form-control form-control-sm"
                                                    wire:model.live="socialMedia.{{ $index }}.url" />
                                            </div>

                                            <div class="row">
                                                <div class="col-6 mb-2">
                                                    <label class="form-label fw-semibold">{{ __('Order') }}</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        wire:model.live="socialMedia.{{ $index }}.order" />
                                                </div>

                                                <div class="col-6 mb-2">
                                                    <label class="form-label fw-semibold">{{ __('Status') }}</label>
                                                    <div class="form-check form-switch">
                                                        <input type="checkbox" class="form-check-input"
                                                            wire:model.live="socialMedia.{{ $index }}.is_active" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <button type="button" class="btn btn-success w-100 mt-3" wire:click="addNewSocialMedia">
                                    <i class="fas fa-plus-circle me-2"></i>{{ __('Add New Social Media') }}
                                </button>
                            </div>
                        </div>

                        <!-- Links Management Section -->
                        <div class="col col-5">
                            <h5 class="mb-3">
                                <button class="btn btn-outline-primary rounded-pill btn-link text-decoration-none" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#linksCollapse" aria-expanded="false"
                                    aria-controls="linksCollapse">
                                    <i class="fas fa-link me-2"></i> {{ __('Manage Links') }}
                                </button>
                            </h5>
                            <div class="collapse shadow-sm rounded p-3 border" id="linksCollapse" wire:ignore.self>
                                <div class="link-items-container">
                                    @foreach ($tempLinks as $index => $link)
                                        <div class="link-item">
                                            <button type="button" class="btn btn-sm btn-danger remove-link text-end"
                                                wire:click="removeLink({{ $index }})">
                                                <i class="fas fa-times"></i>
                                            </button>

                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">{{ __('Link Name') }}</label>
                                                <input type="text" class="form-control form-control-sm"
                                                    wire:model.live="tempLinks.{{ $index }}.name" />
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label fw-semibold">{{ __('Link URL') }}</label>
                                                <input type="url" class="form-control form-control-sm"
                                                    wire:model.live="tempLinks.{{ $index }}.url" />
                                            </div>

                                            <div class="row">
                                                <div class="col-6 mb-2">
                                                    <label class="form-label fw-semibold">{{ __('Open in new tab') }}</label>
                                                    <div class="form-check form-switch">
                                                        <input type="checkbox" class="form-check-input"
                                                            wire:model.live="tempLinks.{{ $index }}.target" />
                                                    </div>
                                                </div>

                                                <div class="col-6 mb-2">
                                                    <label class="form-label fw-semibold">{{ __('Order') }}</label>
                                                    <input type="number" class="form-control form-control-sm"
                                                        wire:model.live="tempLinks.{{ $index }}.order" />
                                                </div>
                                            </div>

                                            <div class="mb-2 d-flex justify-content-between">
                                                <label class="form-label fw-semibold mt-1">{{ __('Status') }}</label>
                                                <div class="form-check form-switch">
                                                    <input type="checkbox" class="form-check-input"
                                                        wire:model.live="tempLinks.{{ $index }}.is_active" />
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <button type="button" class="btn btn-success w-100 mt-3" wire:click="addNewLink">
                                    <i class="fas fa-plus-circle me-2"></i>{{ __('Add New Link') }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center position-fixed end-0 bottom-0 w-100 bg-primary p-3 shadow-lg">
                        <button type="button" class="btn btn-light me-3 m-2 px-4 fw-medium" wire:click="closePage"
                            wire:loading.attr="disabled">
                            <i class="fas fa-times me-2 p-2"></i>{{ __('Cancel') }}
                        </button>
                        <button type="button" class="btn btn-success px-4 m-2 fw-medium" wire:click="create"
                            wire:loading.attr="disabled">
                            <i class="fas fa-save me-2 p-2"></i>{{ __('Save') }}
                        </button>
                    </div>
                </div>

                <!-- Live Preview Section -->
                <div class="col-6 p-4">
                    <h4 class="mb-3 fw-bold text-primary">{{ __('Live Preview') }}</h4>
                    <div class="hero shadow" id="heroSection"
                        style="background-color: {{ $background_color }}; color: {{ $title_color }}; padding: 30px; border-radius: 15px; overflow: hidden;">
                        <div class="text-center">
                            @if ($cover)
                                <div class="cover-container mb-3">
                                    <img src="{{ $cover->temporaryUrl() }}" alt="Cover"
                                        class="cover-img img-fluid rounded"
                                        style="width: 100%; max-height: 220px; object-fit: cover;">
                                </div>
                            @else
                                <div class="cover-container mb-3">
                                    <img src="{{ asset('assets/default_cover.png') }}" alt="Cover"
                                        class="cover-img img-fluid rounded"
                                        style="width: 100%; max-height: 220px; object-fit: cover;">
                                </div>
                            @endif

                            @if ($avatar)
                                <img src="{{ $avatar->temporaryUrl() }}" alt="Profile"
                                    class="profile-img img-fluid rounded-circle shadow"
                                    style="width: 150px; height: 150px; margin-top: -75px; border: 5px solid {{ $background_color }}; object-fit: cover;">
                            @else
                                <img src="{{ asset('assets/default_icon.png') }}" alt="Profile"
                                    class="profile-img img-fluid rounded-circle shadow"
                                    style="width: 150px; height: 150px; margin-top: -75px; border: 5px solid {{ $background_color }}; object-fit: cover;">
                            @endif
                            <h1 class="display-4 fw-bold mt-3" style="color:{{ $title_color }};">
                                {{ $name[app()->getLocale()] ?? __('Default Name') }}</h1>
                            <p class="lead fw-medium" style="color:{{ $job_title_color }};">
                                {{ $job_title[app()->getLocale()] ?? __('Default Job Title') }}</p>
                            <p class="mb-4" style="color:{{ $description_color }};">
                                {{ $description[app()->getLocale()] ?? __('Default description') }}</p>
                        </div>

                        <div class="container mt-4">

                            <footer class="text-center mt-5 mb-5 social-icons">
                                @foreach ($socialMedia ?? [] as $social)
                                    @if ($social['is_active'])
                                        <a href="{{ $social['url'] }}"
                                            style="color: {{ $icon_color }}; margin: 0 10px; background-color: rgba(255, 255, 255, 0.1);" target="_blank">
                                            @php
                                                $iconMap = [
                                                    'facebook' => 'facebook-f',
                                                    'twitter' => 'twitter',
                                                    'instagram' => 'instagram',
                                                    'linkedin' => 'linkedin-in',
                                                    'youtube' => 'youtube',
                                                    'tiktok' => 'tiktok',
                                                    'github' => 'github',
                                                    'dribbble' => 'dribbble',
                                                    'behance' => 'behance',
                                                    'pinterest' => 'pinterest',
                                                    'snapchat' => 'snapchat-ghost',
                                                    'telegram' => 'telegram-plane',
                                                    'whatsapp' => 'whatsapp',
                                                    'discord' => 'discord',
                                                    'other' => 'globe',
                                                ];
                                                $iconClass = $iconMap[$social['platform']] ?? 'link';
                                                $iconPrefix = $iconClass === 'globe' ? 'fas' : 'fab';
                                            @endphp
                                            <i class="{{ $iconPrefix }} fa-{{ $iconClass }}"
                                                style="font-size: 24px;"></i>
                                        </a>
                                    @endif
                                @endforeach
                            </footer>
                            <div class="row">
                                <div class="col-md-10 mx-auto">
                                    <!-- Active Links Preview -->
                                    @foreach ($tempLinks as $link)
                                        @if ($link['is_active'])
                                            <a href="{{ $link['url'] }}" class="btn link-btn mb-3"
                                                style="background-color: {{ $button_color }}; color: {{ $button_text_color }};"
                                                target="{{ $link['target'] ? '_blank' : '_self' }}">
                                                {{ $link['name'] }}
                                            </a>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            // Color panel handling
            const colorPanel = document.getElementById('colorSettings');
            const openBtn = document.getElementById('colorSettingsBtn');
            const closeBtn = document.getElementById('closeColorPanelBtn');
            const closeBtnBottom = document.getElementById('closeColorPanelBtnBottom');

            // Open the color panel
            openBtn.addEventListener('click', function() {
                colorPanel.classList.add('open');
            });

            // Close the color panel
            function closeColorPanel() {
                colorPanel.classList.remove('open');
            }

            closeBtn.addEventListener('click', closeColorPanel);
            closeBtnBottom.addEventListener('click', closeColorPanel);

            // Add color hex value display
            const colorInputs = document.querySelectorAll('input[type="color"]');
            colorInputs.forEach(input => {
                const preview = input.nextElementSibling;
                if (preview && preview.classList.contains('color-preview')) {
                    input.addEventListener('input', function() {
                        preview.textContent = this.value;
                    });
                }
            });

            // Initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        });

        document.addEventListener('livewire:initialized', () => {
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        });

        document.addEventListener('livewire:updated', () => {
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        });
    </script>
@endpush
