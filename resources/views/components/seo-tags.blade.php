{{-- SEO Meta Tags Component --}}
@props([
    'title' => null,
    'description' => null,
    'keywords' => null,
    'image' => null,
    'url' => null,
    'type' => 'website',
    'siteName' => null,
    'author' => null,
    'robots' => 'index, follow',
    'canonical' => null
])

@php
    // Use provided values or fallback to settings
    $seoTitle = $title ?? $meta_title ?? $site_name;
    $seoDescription = $description ?? $meta_description ?? $site_description;
    $seoKeywords = $keywords ?? $meta_keywords;
    $seoImage = $image ?? $og_image;
    $seoUrl = $url ?? url()->current();
    $seoSiteName = $siteName ?? $site_name;
    $seoAuthor = $author ?? $site_name;
    $seoCanonical = $canonical ?? url()->current();
@endphp

{{-- Basic Meta Tags --}}
<title>{{ $seoTitle }}</title>
<meta name="description" content="{{ $seoDescription }}">
<meta name="keywords" content="{{ $seoKeywords }}">
<meta name="author" content="{{ $seoAuthor }}">
<meta name="robots" content="{{ $robots }}">
<link rel="canonical" href="{{ $seoCanonical }}">

{{-- Open Graph Meta Tags --}}
<meta property="og:title" content="{{ $seoTitle }}">
<meta property="og:description" content="{{ $seoDescription }}">
<meta property="og:image" content="{{ $seoImage }}">
<meta property="og:url" content="{{ $seoUrl }}">
<meta property="og:type" content="{{ $type }}">
<meta property="og:site_name" content="{{ $seoSiteName }}">
<meta property="og:locale" content="{{ app()->getLocale() }}">

{{-- Twitter Card Meta Tags --}}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $seoTitle }}">
<meta name="twitter:description" content="{{ $seoDescription }}">
<meta name="twitter:image" content="{{ $seoImage }}">

{{-- Additional Meta Tags --}}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">

{{-- Favicon --}}
<link rel="icon" type="image/x-icon" href="{{ $site_icon }}">
<link rel="shortcut icon" href="{{ $site_icon }}">
<link rel="apple-touch-icon" href="{{ $site_icon }}">

{{-- JSON-LD Structured Data --}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{{ $seoSiteName }}",
    "description": "{{ $seoDescription }}",
    "url": "{{ $seoUrl }}",
    "image": "{{ $seoImage }}",
    "author": {
        "@type": "Organization",
        "name": "{{ $seoAuthor }}"
    }
}
</script>
