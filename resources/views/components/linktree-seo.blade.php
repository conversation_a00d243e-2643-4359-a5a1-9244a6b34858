{{-- LinkTree SEO Meta Tags Component --}}
@props(['linkTree', 'socialMedia' => null])

@php
    $currentLocale = app()->getLocale();

    // LinkTree SEO data
    $linkTreeTitle = $linkTree->getTranslation('name', $currentLocale);
    $linkTreeDescription =
        $linkTree->getTranslation('description', $currentLocale) ?:
        $linkTree->getTranslation('job_title', $currentLocale);
    $linkTreeJobTitle = $linkTree->getTranslation('job_title', $currentLocale);
    $linkTreeKeywords = is_array($linkTree->keywords)
        ? $linkTree->keywords[$currentLocale] ?? implode(', ', array_values($linkTree->keywords))
        : $linkTree->keywords;

    // Images
    $linkTreeAvatar = $linkTree->getFirstMediaUrl('avatar');
    $linkTreeCover = $linkTree->getFirstMediaUrl('cover');
    $linkTreeImage = $linkTreeAvatar ?: $linkTreeCover ?: $og_image;

    // Page data
    $pageTitle = $linkTreeTitle . ' - ' . $site_name;
    $pageUrl = url()->current();
@endphp

{{-- Basic Meta Tags --}}
<title>{{ $pageTitle }}</title>
<meta name="description" content="{{ $linkTreeDescription }}">
<meta name="keywords" content="{{ $linkTreeKeywords }}">
<meta name="author" content="{{ $linkTreeTitle }}">
<meta name="robots" content="index, follow">
<link rel="canonical" href="{{ $pageUrl }}">

{{-- Open Graph Meta Tags --}}
<meta property="og:title" content="{{ $pageTitle }}">
<meta property="og:description" content="{{ $linkTreeDescription }}">
<meta property="og:image" content="{{ $linkTreeImage }}">
<meta property="og:url" content="{{ $pageUrl }}">
<meta property="og:type" content="profile">
<meta property="og:site_name" content="{{ $site_name }}">
<meta property="profile:first_name" content="{{ $linkTreeTitle }}">
<meta property="profile:username" content="{{ $linkTree->slug }}">

{{-- Twitter Card Meta Tags --}}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $pageTitle }}">
<meta name="twitter:description" content="{{ $linkTreeDescription }}">
<meta name="twitter:image" content="{{ $linkTreeImage }}">
<meta name="twitter:creator" content="@{{ $linkTree - > slug }}">

{{-- Additional Meta Tags --}}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">

{{-- Favicon --}}
<link rel="icon" type="image/x-icon" href="{{ $site_icon }}">
<link rel="shortcut icon" href="{{ $site_icon }}">
<link rel="apple-touch-icon" href="{{ $site_icon }}">

{{-- JSON-LD Structured Data for Person --}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "{{ $linkTreeTitle }}",
    "description": "{{ $linkTreeDescription }}",
    "url": "{{ $pageUrl }}",
    "identifier": "{{ $linkTree->slug }}",
    @if($linkTreeJobTitle)
    "jobTitle": "{{ $linkTreeJobTitle }}",
    @endif
    @if($linkTreeImage)
    "image": "{{ $linkTreeImage }}",
    @endif
    @if($socialMedia && $socialMedia->count() > 0)
    "sameAs": [
        @foreach($socialMedia as $social)
            @if($social->is_active && $social->url)
                "{{ $social->url }}"{{ !$loop->last ? ',' : '' }}
            @endif
        @endforeach
    ],
    @endif
    "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "{{ $pageUrl }}"
    }
}
</script>
