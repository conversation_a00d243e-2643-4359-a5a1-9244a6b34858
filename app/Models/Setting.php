<?php

namespace App\Models;

use App\Traits\Loggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>tie\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;
use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Translatable\HasTranslations;

class Setting extends Model implements HasMedia
{
    use HasFactory;
    use HasTranslations;
    use Loggable;
    use InteractsWithMedia;
    
    protected $fillable = [
        // Basic Site Information
        'site_name', 'site_description', 'site_keywords', 'site_tagline', 'site_url',
        'admin_email', 'support_email', 'noreply_email',
        
        // Branding & Media
        'site_logo', 'site_logo_dark', 'site_icon', 'site_favicon', 'og_image',
        
        // Language & Localization
        'default_language', 'timezone', 'date_format', 'time_format',
        'currency', 'currency_symbol', 'currency_position',
        
        // Contact Information
        'contact_phone', 'contact_whatsapp', 'contact_address', 'latitude', 'longitude',
        
        // Social Media
        'facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url',
        'youtube_url', 'tiktok_url', 'github_url',
        
        // SEO Settings
        'meta_title', 'meta_description', 'meta_keywords',
        'google_analytics_id', 'google_tag_manager_id', 'facebook_pixel_id',
        'custom_head_code', 'custom_body_code', 'robots_txt',
        
        // Security Settings
        'maintenance_mode', 'maintenance_message', 'user_registration',
        'email_verification', 'two_factor_auth', 'session_lifetime',
        'allowed_file_types', 'max_file_size',
        
        // Business
        'business_name', 'terms_of_service', 'privacy_policy',
        
        // Theme
        'default_theme', 'theme_colors', 'dark_mode_enabled',
    ];
    
    public $translatable = [
        'site_name', 'site_description', 'site_keywords', 'site_tagline',
        'contact_address', 'meta_title', 'meta_description', 'meta_keywords',
        'maintenance_message', 'business_name', 'terms_of_service', 'privacy_policy'
    ];
    
    protected $casts = [
        'maintenance_mode' => 'boolean',
        'user_registration' => 'boolean',
        'email_verification' => 'boolean',
        'two_factor_auth' => 'boolean',
        'dark_mode_enabled' => 'boolean',
        'allowed_file_types' => 'array',
        'theme_colors' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    public function language()
    {
        return $this->belongsTo(Language::class, 'default_language', 'id');
    }
    
    public static function get($key, $default = null)
    {
        $setting = static::first();
        return $setting ? ($setting->$key ?? $default) : $default;
    }
    
    public static function set($key, $value)
    {
        $setting = static::firstOrCreate([]);
        $setting->update([$key => $value]);
        return $setting;
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('site_logo_ar')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('site_logo_en')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('site_favicon')
            ->singleFile()
            ->acceptsMimeTypes(['image/x-icon', 'image/png']);

        $this->addMediaCollection('og_image')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10);
    }
}
