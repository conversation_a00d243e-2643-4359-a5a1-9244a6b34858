<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsHelper
{
    protected static $settings;

    public static function get($key, $default = null)
    {
        if (static::$settings === null) {
            $cacheEnabled = config('app.cache_enabled', true);
            if ($cacheEnabled) {
                static::$settings = Cache::remember('site_settings', 3600, function () {
                    return Setting::first();
                });
            } else {
                static::$settings = Setting::first();
            }
        }

        return static::$settings ? (static::$settings->$key ?? $default) : $default;
    }

    public static function set($key, $value)
    {
        $setting = Setting::firstOrCreate([]);
        $setting->update([$key => $value]);

        // Clear cache
        static::clearCache();

        return $setting;
    }

    public static function siteName($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $siteName = static::get('site_name', []);
        if (is_array($siteName)) {
            return $siteName[$locale] ?? $siteName['en'] ?? 'ProfileProject';
        }

        return $siteName ?: 'ProfileProject';
    }

    public static function siteDescription($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $description = static::get('site_description', []);
        if (is_array($description)) {
            return $description[$locale] ?? $description['en'] ?? '';
        }

        return $description ?: '';
    }

    public static function formatCurrency($amount)
    {
        $symbol = static::get('currency_symbol', '$');
        $position = static::get('currency_position', 'before');

        return $position === 'before' ? $symbol.$amount : $amount.' '.$symbol;
    }

    public static function isMaintenanceMode()
    {
        return static::get('maintenance_mode', false);
    }

    public static function isRegistrationEnabled()
    {
        return static::get('user_registration', true);
    }

    public static function getThemeColors()
    {
        return static::get('theme_colors', [
            'primary' => '#3B82F6',
            'secondary' => '#6B7280',
            'success' => '#10B981',
            'warning' => '#F59E0B',
            'danger' => '#EF4444',
        ]);
    }

    public static function getSocialLinks()
    {
        return [
            'facebook' => static::get('facebook_url'),
            'twitter' => static::get('twitter_url'),
            'instagram' => static::get('instagram_url'),
            'linkedin' => static::get('linkedin_url'),
            'youtube' => static::get('youtube_url'),
            'tiktok' => static::get('tiktok_url'),
            'github' => static::get('github_url'),
        ];
    }

    /**
     * Get SEO meta title from settings
     */
    public static function getMetaTitle($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $metaTitle = static::get('meta_title', []);

        if (is_string($metaTitle)) {
            $metaTitle = json_decode($metaTitle, true) ?: [];
        }

        if (is_array($metaTitle)) {
            return $metaTitle[$locale] ?? $metaTitle['en'] ?? static::siteName($locale);
        }

        return $metaTitle ?: static::siteName($locale);
    }

    /**
     * Get SEO meta description from settings
     */
    public static function getMetaDescription($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $metaDescription = static::get('meta_description', []);

        if (is_string($metaDescription)) {
            $metaDescription = json_decode($metaDescription, true) ?: [];
        }

        if (is_array($metaDescription)) {
            return $metaDescription[$locale] ?? $metaDescription['en'] ?? static::siteDescription($locale);
        }

        return $metaDescription ?: static::siteDescription($locale);
    }

    /**
     * Get SEO meta keywords from settings
     */
    public static function getMetaKeywords($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $metaKeywords = static::get('meta_keywords', []);

        if (is_string($metaKeywords)) {
            $metaKeywords = json_decode($metaKeywords, true) ?: [];
        }

        if (is_array($metaKeywords)) {
            return $metaKeywords[$locale] ?? $metaKeywords['en'] ?? '';
        }

        return $metaKeywords ?: '';
    }

    /**
     * Get Google Analytics ID from settings
     */
    public static function getGoogleAnalyticsId()
    {
        return static::get('google_analytics_id');
    }

    /**
     * Get Google Tag Manager ID from settings
     */
    public static function getGoogleTagManagerId()
    {
        return static::get('google_tag_manager_id');
    }

    /**
     * Get Facebook Pixel ID from settings
     */
    public static function getFacebookPixelId()
    {
        return static::get('facebook_pixel_id');
    }

    /**
     * Get custom head code from settings
     */
    public static function getCustomHeadCode()
    {
        return static::get('custom_head_code');
    }

    /**
     * Get custom body code from settings
     */
    public static function getCustomBodyCode()
    {
        return static::get('custom_body_code');
    }

    /**
     * Get robots.txt content from settings
     */
    public static function getRobotsTxt()
    {
        return static::get('robots_txt');
    }

    /**
     * Get site URL from settings
     */
    public static function getSiteUrl()
    {
        return static::get('site_url', config('app.url'));
    }

    /**
     * Get Open Graph image URL
     */
    public static function getOgImage()
    {
        try {
            // أولاً: تحقق من إعدادات الهوم
            $homeData = static::getHomeData();
            if ($homeData && is_object($homeData)) {
                $ogImageUrl = $homeData->getFirstMediaUrl('og_image');
                if ($ogImageUrl && !empty($ogImageUrl)) {
                    return $ogImageUrl;
                }

                // إذا لم توجد صورة OG محددة، استخدم صورة الهيرو
                $heroImageUrl = $homeData->getFirstMediaUrl('hero_image');
                if ($heroImageUrl && !empty($heroImageUrl)) {
                    return $heroImageUrl;
                }
            }

            // ثانياً: تحقق من إعدادات Settings
            $setting = static::get('id') ? Setting::first() : null;
            if ($setting && is_object($setting)) {
                $settingsOgImageUrl = $setting->getFirstMediaUrl('og_image');
                if ($settingsOgImageUrl && !empty($settingsOgImageUrl)) {
                    return $settingsOgImageUrl;
                }
            }

            // ثالثاً: استخدم الشعار كبديل
            return static::getSiteLogo();
        } catch (\Exception $e) {
            return static::getSiteLogo();
        }
    }

    public static function getContactInfo()
    {
        return [
            'phone' => static::get('contact_phone'),
            'whatsapp' => static::get('contact_whatsapp'),
            'email' => static::get('admin_email'),
            'address' => static::get('contact_address'),
            'latitude' => static::get('latitude'),
            'longitude' => static::get('longitude'),
        ];
    }

    public static function clearCache()
    {
        Cache::forget('site_settings');
        Cache::forget('home_data');
        static::$settings = null;
    }

    public static function getHomeData()
    {
        try {
            $cacheEnabled = static::get('cache_enabled', true);
            if ($cacheEnabled) {
                return Cache::remember('home_data', 1800, function () {
                    $homeService = new \App\Services\Front\HomeService();
                    $result = $homeService->getHomeData();

                    return $result && isset($result->Status) && $result->Status ? $result->data : null;
                });
            } else {
                $homeService = new \App\Services\Front\HomeService();
                $result = $homeService->getHomeData();

                return $result && isset($result->Status) && $result->Status ? $result->data : null;
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getSiteLogo($language = null)
    {
        $language = $language ?: app()->getLocale();
        $setting = static::get('id') ? Setting::first() : null;

        if ($setting) {
            // Try language-specific logo first
            if ($language === 'ar' && $setting->getFirstMediaUrl('site_logo_ar')) {
                return $setting->getFirstMediaUrl('site_logo_ar');
            }
            if ($language === 'en' && $setting->getFirstMediaUrl('site_logo_en')) {
                return $setting->getFirstMediaUrl('site_logo_en');
            }
            // Fallback to default logo
            if ($setting->site_logo) {
                return asset($setting->site_logo);
            }
        }

        return asset('assets/images/logo.png');
    }

    /**
     * Get site favicon
     * يتحقق من الهوم أولاً، ثم من Settings، ثم الافتراضي.
     */
    public static function getSiteFavicon()
    {
        try {
            // أولاً: تحقق من إعدادات الهوم
            $homeData = static::getHomeData();
            if ($homeData && is_object($homeData)) {
                // تحقق من favicon في الهوم (يدعم PNG وأنواع أخرى)
                $faviconUrl = $homeData->getFirstMediaUrl('favicon');
                if ($faviconUrl && !empty($faviconUrl)) {
                    return $faviconUrl;
                }
            }

            // ثانياً: تحقق من إعدادات Settings
            $setting = static::get('id') ? Setting::first() : null;
            if ($setting && is_object($setting)) {
                // Try to get uploaded favicon from settings
                $settingsFaviconUrl = $setting->getFirstMediaUrl('site_favicon');
                if ($settingsFaviconUrl && !empty($settingsFaviconUrl)) {
                    return $settingsFaviconUrl;
                }
            }

            // ثالثاً: الافتراضي
            return asset('favicon.ico');
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، إرجاع الافتراضي
            return asset('favicon.ico');
        }
    }
}
