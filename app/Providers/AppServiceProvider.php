<?php

namespace App\Providers;

use App\Models\Language;
use App\Models\LinkTree;
use App\Models\Message;
use App\Models\User;
use App\Observers\LinkTreeObserver;
use App\Observers\ModelLogObserver;
use App\Services\Front\HomeService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Laravel\Sanctum\PersonalAccessToken;
use Laravel\Sanctum\Sanctum;
use Livewire\Livewire;

class AppServiceProvider extends ServiceProvider
{
    protected $HomeService;

    // public function __construct()
    // {
    // }
    /**
     * Register any application services.
     */
    public function register(): void
    {
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->HomeService = new HomeService();
        LinkTree::observe(LinkTreeObserver::class);

        // Register observers for logging
        User::observe(ModelLogObserver::class);
        // Session::put('lang', 'ar');
        // App::setLocale(strtolower('ar')); // remove this line if active multi languages
        Schema::defaultStringLength(191);
        Sanctum::usePersonalAccessTokenModel(PersonalAccessToken::class);
        
        // Register Livewire components
        Livewire::component('app.http.controllers.web.dashboard', \App\Http\Controllers\Web\Dashboard\Index::class);
        Livewire::component('dashboard.index', \App\Http\Controllers\Web\Dashboard\Index::class);

        View::composer('*', function ($view) {
            try {
                $currentlanguage = app()->getLocale();
                $availablelanguages = Language::where('status', 1)->get();
                $language = Language::where('status', 1)->where('short', $currentlanguage)->first();
                $languageName = $language ? $language->name : null;
                $languageShort = $language ? $language->short : null;
                $languageDir = $language ? $language->dir : null;

                // Get messages for authenticated users only
                $myMessages = Auth::check() ? Message::where('user_id', Auth::user()->id)->get() : collect();

                // Get home data - always load for now to fix display issues
                $homeData = optional($this->HomeService)->getHomeData();
                $front = $homeData && $homeData->Status ? $homeData->data : null;

                // Check if current request is for LinkTree and get LinkTree data
                $linkTree = null;
                $socialMedia = null;
                $links = null;

                if (request()->route() && request()->route()->getName() === 'linktree.show') {
                    $slug = request()->route('slug');
                    if ($slug) {
                        $linkTreeData = \App\Models\LinkTree::where('slug', $slug)
                            ->where('is_active', true)
                            ->with(['links' => function ($query) {
                                $query->where('is_active', true)->orderBy('order');
                            }, 'socialMedia' => function ($query) {
                                $query->where('is_active', true)->orderBy('order');
                            }])
                            ->first();

                        if ($linkTreeData) {
                            $linkTree = $linkTreeData;
                            $socialMedia = $linkTreeData->socialMedia;
                            $links = $linkTreeData->links;
                        }
                    }
                }

                $view->with([
                    // Site settings from SettingsHelper
                    'site_name' => \App\Helpers\SettingsHelper::siteName($currentlanguage),
                    'site_description' => \App\Helpers\SettingsHelper::siteDescription($currentlanguage),
                    'site_keywords' => \App\Helpers\SettingsHelper::get('site_keywords', [])[$currentlanguage] ?? '',
                    'site_logo' => \App\Helpers\SettingsHelper::getSiteLogo($currentlanguage),
                    'site_icon' => \App\Helpers\SettingsHelper::getSiteFavicon(),
                    'register_status' => \App\Helpers\SettingsHelper::isRegistrationEnabled(),

                    // SEO settings from SettingsHelper
                    'meta_title' => \App\Helpers\SettingsHelper::getMetaTitle($currentlanguage),
                    'meta_description' => \App\Helpers\SettingsHelper::getMetaDescription($currentlanguage),
                    'meta_keywords' => \App\Helpers\SettingsHelper::getMetaKeywords($currentlanguage),
                    'og_image' => \App\Helpers\SettingsHelper::getOgImage(),
                    'site_url' => \App\Helpers\SettingsHelper::getSiteUrl(),
                    'google_analytics_id' => \App\Helpers\SettingsHelper::getGoogleAnalyticsId(),
                    'google_tag_manager_id' => \App\Helpers\SettingsHelper::getGoogleTagManagerId(),
                    'facebook_pixel_id' => \App\Helpers\SettingsHelper::getFacebookPixelId(),
                    'custom_head_code' => \App\Helpers\SettingsHelper::getCustomHeadCode(),
                    'custom_body_code' => \App\Helpers\SettingsHelper::getCustomBodyCode(),

                    // Language settings
                    'availablelanguages' => $availablelanguages,
                    'languageName' => $languageName,
                    'languageShort' => $languageShort,
                    'languageDir' => $languageDir ?: ($currentlanguage === 'ar' ? 'rtl' : 'ltr'),
                    'test_lang' => $currentlanguage,

                    // Home data (only when needed)
                    'front' => $front,
                    'display_name' => is_object($front) ? $front->meta_title : \App\Helpers\SettingsHelper::siteName($currentlanguage),

                    // User messages
                    'myMessages' => $myMessages,

                    // Additional settings
                    'contactInfo' => \App\Helpers\SettingsHelper::getContactInfo(),
                    'socialLinks' => \App\Helpers\SettingsHelper::getSocialLinks(),
                    'themeColors' => \App\Helpers\SettingsHelper::getThemeColors(),

                    // LinkTree data (if available)
                    'linkTree' => $linkTree,
                    'socialMedia' => $socialMedia,
                    'links' => $links,
                ]);
            } catch (\Exception $e) {
                // Fallback values in case of errors
                $currentlanguage = app()->getLocale();
                $view->with([
                    'site_name' => config('app.name', 'ProfileProject'),
                    'site_description' => '',
                    'availablelanguages' => collect(),
                    'myMessages' => collect(),
                    'front' => null,
                    'languageDir' => $currentlanguage === 'ar' ? 'rtl' : 'ltr',
                    'languageName' => $currentlanguage === 'ar' ? 'العربية' : 'English',
                    'languageShort' => $currentlanguage,
                    'test_lang' => $currentlanguage,
                ]);
            }
        });
    }
}
