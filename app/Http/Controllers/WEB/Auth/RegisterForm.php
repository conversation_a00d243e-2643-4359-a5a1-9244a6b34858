<?php

namespace App\Http\Controllers\WEB\Auth;

use App\Models\Plan;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class RegisterForm extends Component
{
    public $name;
    public $email;
    public $phone;
    public $password;
    public $password_confirmation;
    public $terms;

    // For username checker
    public $username = '';
    public $isUsernameAvailable = false;
    public $usernameMessage = '';
    public $usernameClass = '';

    protected $rules = [
        'name' => 'required|string|max:255',
        'username' => 'required|string|min:4|max:255|regex:/^[a-zA-Z0-9_]+$/|unique:users,username',
        'email' => 'required|string|email|max:255|unique:users',
        'phone' => 'required|string|max:15',
        'password' => 'required|string|min:8|confirmed',
    ];

    protected function messages()
    {
        return [
            'name.required' => __('name_required'),
            'username.required' => __('username_required'),
            'username.min' => __('username_min'),
            'username.unique' => __('username_unique'),
            'username.regex' => __('username_regex'),
            'email.required' => __('email_required'),
            'email.email' => __('email_valid'),
            'email.unique' => __('email_unique'),
            'phone.required' => __('phone_required'),
            'password.required' => __('password_required'),
            'password.min' => __('password_min'),
            'password.confirmed' => __('password_confirmed'),
        ];
    }

    public function updatedUsername()
    {
        if (strlen($this->username) < 4) {
            $this->usernameMessage = __('username_must_less_than_4_digits');
            $this->usernameClass = 'text-red-500';
            $this->isUsernameAvailable = false;

            return;
        }

        if (!preg_match('/^[a-zA-Z0-9_]+$/', $this->username)) {
            $this->usernameMessage = __('username_invalid_characters');
            $this->usernameClass = 'text-red-500';
            $this->isUsernameAvailable = false;

            return;
        }

        $exists = User::where('username', $this->username)->exists();
        $this->isUsernameAvailable = !$exists;
        $this->usernameMessage = $exists ? __('username_unavailable') : __('username_available');
        $this->usernameClass = $exists ? 'text-red-500' : 'text-green-500';
    }

    public function register()
    {
        $this->validate();

        if (!$this->isUsernameAvailable) {
            return;
        }

        $user = User::create([
            'name' => $this->name,
            'username' => $this->username,
            'email' => $this->email,
            'phone' => $this->phone,
            'password' => Hash::make($this->password),
        ]);

        // لم يعد يتم إنشاء LinkTree هنا، سيتم إنشاؤه عند الاشتراك في الخطة المجانية

        event(new Registered($user));

        Auth::login($user);

        // بعد التسجيل، نوجه المستخدم إلى صفحة الاشتراكات لاختيار خطة
        // ابحث عن الخطة المجانية
        $freePlan = Plan::where('price', 0)
            ->where('is_active', true)
            ->where('is_public', true)
            ->first();

        if ($freePlan) {
            // إذا كانت هناك خطة مجانية، توجيه المستخدم إلى صفحة الاشتراك بالخطة المجانية
            return redirect()->route('subscriptions.subscribe', ['planId' => $freePlan->id]);
        }

        // إذا لم تكن هناك خطة مجانية، توجيه المستخدم إلى لوحة التحكم
        return redirect()->route('dashboard');
    }

    public function render()
    {
        return view('pages.auth.register-form')
            ->layout('layouts.auth');
    }
}
