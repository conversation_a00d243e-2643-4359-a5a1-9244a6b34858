<?php

namespace App\Http\Controllers\WEB\Front\V1;

use App\Models\LinkTree as LinkTreeModel;
use App\Services\AnalyticsService;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.guest')]
class LinkTree extends Component
{
    public $slug;
    public $linkTree;
    public $socialMedia;
    public $links;
    
    protected $analyticsService;
    
    public function boot()
    {
        $this->analyticsService = new AnalyticsService();
    }

    public function mount($slug)
    {
        // dd($slug);
        $this->slug = $slug;
        $this->linkTree = LinkTreeModel::where('slug', $slug)
            ->where('is_active', true)
            ->with(['links' => function ($query) {
                $query->where('is_active', true)
                    ->orderBy('order');
            }, 'socialMedia' => function ($query) {
                $query->where('is_active', true)
                    ->orderBy('order');
            }])
            ->firstOrFail();

        $this->links = $this->linkTree->links;
        $this->socialMedia = $this->linkTree->socialMedia;
    }
    
    public function trackLinkClick($linkUrl, $linkTitle)
    {
        $this->analyticsService->trackLinkClick(
            $this->linkTree->id,
            $linkUrl,
            $linkTitle
        );
    }

    public function render()
    {
        return view('pages.front.v1.link-tree');
    }
}
