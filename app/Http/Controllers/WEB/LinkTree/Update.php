<?php

namespace App\Http\Controllers\WEB\LinkTree;

use App\Services\LinkTreeService;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class Update extends Component
{
    use LivewireAlert;
    use WithFileUploads;
    protected $LinkTreeService;
    public $linkTree;
    public $linkTreeId;
    public $name = [];
    public $slug;
    public $description = [];
    public $job_title = [];
    public $keywords = [];
    public $default_language;
    public $title_color;
    public $description_color;
    public $job_title_color;
    public $icon_color;
    public $button_text_color;
    public $button_color;
    public $background_color;
    public $is_active;
    public $oldAvatar;
    public $social;
    public $oldCover;
    public $avatar;
    public $cover;
    public $links = [];
    public $tempLinks = []; // For managing links in the UI
    public $socialMedia = []; // For managing social media

    // للتحقق من الـ slug
    public $isSlugAvailable = true;
    public $slugMessage = '';
    public $slugClass = '';

    public function __construct()
    {
        $this->LinkTreeService = new LinkTreeService();
    }

    public function updatedSlug()
    {
        $this->checkSlug();
    }

    private function checkSlug()
    {
        if (empty($this->slug)) {
            $this->slugMessage = __('slug_required');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;

            return;
        }

        if (strlen($this->slug) < 4) {
            $this->slugMessage = __('slug_min_length');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;

            return;
        }

        if (!preg_match('/^[a-zA-Z0-9-]+$/', $this->slug)) {
            $this->slugMessage = __('slug_invalid_characters');
            $this->slugClass = 'text-red-500';
            $this->isSlugAvailable = false;

            return;
        }

        // تحقق مما إذا كان الـ slug موجود بالفعل، مع استثناء الـ slug الحالي
        $exists = \App\Models\LinkTree::where('slug', $this->slug)
            ->where('id', '!=', $this->linkTreeId)
            ->exists();

        $this->isSlugAvailable = !$exists;
        $this->slugMessage = $exists ? __('slug_unavailable') : __('slug_available');
        $this->slugClass = $exists ? 'text-red-500' : 'text-green-500';
    }

    protected function rules()
    {
        return [
            'name' => 'required|array',
            'slug' => 'required|string',
            'description' => 'required|array',
            'job_title' => 'required|array',
            'keywords' => 'nullable|array',
            'default_language' => 'required|exists:languages,id',
            'title_color' => 'nullable|string',
            'description_color' => 'nullable|string',
            'job_title_color' => 'nullable|string',
            'icon_color' => 'nullable|string',
            'button_text_color' => 'nullable|string',
            'button_color' => 'nullable|string',
            'background_color' => 'nullable|string',
            'is_active' => 'required|boolean',

            'tempLinks' => 'nullable|array',

            'tempLinks.*.name' => 'required|array',
            'tempLinks.*.url' => 'required|string',
            'tempLinks.*.target' => 'nullable|boolean',
            'tempLinks.*.order' => 'nullable|integer',
            'tempLinks.*.is_active' => 'nullable|boolean',
            'socialMedia' => 'nullable|array',
            'socialMedia.*.platform' => 'required|string',
            'socialMedia.*.url' => 'required|url',
            'socialMedia.*.order' => 'nullable|integer',
            'socialMedia.*.is_active' => 'nullable|boolean',
        ];
    }

    public function mount($id)
    {
        // Initialize arrays to prevent null errors
        $this->tempLinks = [];
        $this->socialMedia = [];

        $result = $this->LinkTreeService->singleLinkTree($id);
        if ($result->Status) {
            $this->isSlugAvailable = true; // مبدئياً نفترض أن الـ slug متاح لأنه القيمة الحالية
            $this->linkTreeId = $id;
            $this->linkTree = $result->data;
            $this->name = $this->linkTree->getTranslations('name') ?? [];
            $this->slug = $this->linkTree->slug;
            $this->description = $this->linkTree->getTranslations('description') ?? [];
            $this->job_title = $this->linkTree->getTranslations('job_title') ?? [];
            $this->keywords = $this->linkTree->keywords;
            $this->default_language = $this->linkTree->default_language;
            $this->title_color = $this->linkTree->title_color;
            $this->description_color = $this->linkTree->description_color;
            $this->job_title_color = $this->linkTree->job_title_color;
            $this->icon_color = $this->linkTree->icon_color;
            $this->button_text_color = $this->linkTree->button_text_color;
            $this->button_color = $this->linkTree->button_color;
            $this->background_color = $this->linkTree->background_color;
            $this->is_active = $this->linkTree->is_active;
            $this->links = $this->linkTree->links;
            $this->oldAvatar = $this->linkTree->getFirstMediaUrl('avatar');
            $this->oldCover = $this->linkTree->getFirstMediaUrl('cover');            // Convert existing links to tempLinks format for editing
            $this->tempLinks = [];
            foreach ($this->linkTree->links as $link) {
                $this->tempLinks[] = [
                    'id' => $link->id,
                    'name' => $link->name,
                    'url' => $link->url,
                    'target' => $link->target === '_blank',
                    'order' => $link->order,
                    'is_active' => $link->is_active,
                ];
            }

            // Convert existing linkTree media to linkTreeMedia format for editing
            $this->socialMedia = [];
            if ($this->linkTree->socialMedia->isNotEmpty()) {
                foreach ($this->linkTree->socialMedia as $socialMedia) {
                    $this->socialMedia[] = [
                        'id' => $socialMedia->id,
                        'platform' => $socialMedia->getTranslation('name', app()->getLocale()) ?? [],
                        'url' => $socialMedia->url,
                        'order' => $socialMedia->order,
                        'is_active' => $socialMedia->is_active,
                    ];
                }
            }

            // // If no social media exists, add default ones
            // if (empty($this->socialMedia)) {
            //     $this->addNewSocialMedia();
            // }
        }
    }

    // Add some default social media platforms
    private function addDefaultSocialMedia()
    {
        $defaultPlatforms = ['facebook', 'twitter', 'instagram', 'linkedin'];

        foreach ($defaultPlatforms as $index => $platform) {
            $this->socialMedia[] = [
                'id' => null,
                'platform' => $platform,
                'url' => 'https://'.$platform.'.com/',
                'order' => $index + 1,
                'is_active' => false,
            ];
        }
    }

    public function closePage()
    {
        return redirect()->route('linktree.index');
    }

    public function addNewLink()
    {
        $this->tempLinks[] = [
            'id' => null,
            'name' => __('New Link'),
            'url' => 'https://',
            'target' => true,
            'order' => count($this->tempLinks) + 1,
            'is_active' => true,
        ];
    }

    public function removeLink($index)
    {
        if (isset($this->tempLinks[$index])) {
            array_splice($this->tempLinks, $index, 1);
        }
    }

    public function addNewSocialMedia()
    {
        $platformUrls = [
            'facebook' => 'https://facebook.com/',
            'twitter' => 'https://twitter.com/',
            'instagram' => 'https://instagram.com/',
            'linkedin' => 'https://linkedin.com/',
            'youtube' => 'https://youtube.com/',
            'github' => 'https://github.com/',
            'dribbble' => 'https://dribbble.com/',
            'behance' => 'https://behance.net/',
            'pinterest' => 'https://pinterest.com/',
            'snapchat' => 'https://snapchat.com/',
            'telegram' => 'https://telegram.org/',
            'whatsapp' => 'https://api.whatsapp.com/send?phone=', // WhatsApp link for messaging
            'discord' => 'https://discord.com/',
            'other' => '',
        ];

        $selectedPlatform = 'facebook'; // Replace this with the selected platform from the dropdown
        $url = $platformUrls[$selectedPlatform] ?? '';

        // If the selected platform is WhatsApp, append a phone number placeholder
        if ($selectedPlatform === 'whatsapp') {
            $url .= '1234567890'; // Replace '1234567890' with the desired phone number
        }

        $this->socialMedia[] = [
            'platform' => $selectedPlatform,
            'url' => $url,
            'order' => count($this->socialMedia) + 1,
            'is_active' => true,
        ];
    }

    public function removeSocialMedia($index)
    {
        if (isset($this->socialMedia[$index])) {
            array_splice($this->socialMedia, $index, 1);
        }
    }

    // add updateSocialMediaUrl function
    public function updateSocialMediaUrl($index)
    {
        $platform = $this->socialMedia[$index]['platform'];
        $platformUrls = [
            'facebook' => 'https://facebook.com/',
            'twitter' => 'https://twitter.com/',
            'instagram' => 'https://instagram.com/',
            'linkedin' => 'https://linkedin.com/',
            'youtube' => 'https://youtube.com/',
            'github' => 'https://github.com/',
            'dribbble' => 'https://dribbble.com/',
            'behance' => 'https://behance.net/',
            'pinterest' => 'https://pinterest.com/',
            'snapchat' => 'https://snapchat.com/',
            'telegram' => 'https://telegram.org/',
            'whatsapp' => 'https://api.whatsapp.com/send?phone=', // WhatsApp link for messaging
            'discord' => 'https://discord.com/',
            'other' => '',
        ];
        $url = $platformUrls[$platform] ?? '';
        // If the selected platform is WhatsApp, append a phone number placeholder
        if ($platform === 'whatsapp') {
            $url .= '1234567890'; // Replace '1234567890' with the desired phone number
        }
        $this->socialMedia[$index]['url'] = $url;
    }

    public function update()
    {
        // تحقق من توفر الـ slug قبل التحديث
        if (!$this->isSlugAvailable) {
            $this->alert('error', __('Please fix the errors before saving'));

            return;
        }

        // $this->validate();
        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'job_title' => $this->job_title,
            'keywords' => $this->keywords,
            'default_language' => $this->default_language,
            'title_color' => $this->title_color,
            'description_color' => $this->description_color,
            'job_title_color' => $this->job_title_color,
            'icon_color' => $this->icon_color,
            'button_text_color' => $this->button_text_color,
            'button_color' => $this->button_color,
            'background_color' => $this->background_color,
            'is_active' => $this->is_active,
            'links' => $this->tempLinks,
            'social_media' => $this->socialMedia,
            'avatar' => $this->avatar,
            'cover' => $this->cover,
        ];

        $result = $this->LinkTreeService->updateLinkTree($this->linkTreeId, $data);
        if ($result->Status) {
            $this->closePage();
            $this->alert('success', __('Data updated successfully'));
            $this->dispatch('refreshParent');
        }
    }

    public function render()
    {
        return view('pages.link-tree.update');
    }
}
