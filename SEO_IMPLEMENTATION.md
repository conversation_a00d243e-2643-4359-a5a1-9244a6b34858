# SEO Implementation Guide

## Overview
تم تحديث النظام ليقرأ جميع إعدادات السيو من جدول الإعدادات (Settings) بدلاً من الاعتماد على بيانات الصفحة الرئيسية فقط.

## Features Added

### 1. SEO Helper Functions
تم إضافة دوال جديدة في `SettingsHelper` لقراءة إعدادات السيو:

- `getMetaTitle($locale)` - عنوان الصفحة
- `getMetaDescription($locale)` - وصف الصفحة  
- `getMetaKeywords($locale)` - كلمات مفتاحية
- `getGoogleAnalyticsId()` - معرف Google Analytics
- `getGoogleTagManagerId()` - معرف Google Tag Manager
- `getFacebookPixelId()` - معرف Facebook Pixel
- `getCustomHeadCode()` - كود HTML مخصص للـ head
- `getCustomBodyCode()` - كود HTML مخصص للـ body
- `getRobotsTxt()` - محتوى robots.txt
- `getSiteUrl()` - رابط الموقع
- `getOgImage()` - صورة Open Graph

### 2. Updated Layouts
تم تحديث جميع الـ layouts الرئيسية:

- `layouts/v1.blade.php` - الواجهة الأمامية الرئيسية
- `layouts/app.blade.php` - لوحة التحكم
- `layouts/guest.blade.php` - صفحات الضيوف

### 3. SEO Components
تم إنشاء components قابلة لإعادة الاستخدام:

- `components/seo-tags.blade.php` - جميع meta tags
- `components/analytics.blade.php` - كود التتبع للـ head
- `components/body-tracking.blade.php` - كود التتبع للـ body

### 4. Settings Integration
تم تحديث `AppServiceProvider` لتمرير جميع إعدادات السيو للـ views تلقائياً.

## Usage Examples

### Using SEO Components
```blade
{{-- في أي layout --}}
<x-seo-tags 
    title="عنوان مخصص"
    description="وصف مخصص"
    keywords="كلمات, مفتاحية, مخصصة"
/>

<x-analytics />

{{-- في بداية body --}}
<x-body-tracking />
```

### Using Variables Directly
```blade
{{-- في head section --}}
<title>{{ $meta_title ?? $site_name }}</title>
<meta name="description" content="{{ $meta_description }}">
<meta name="keywords" content="{{ $meta_keywords }}">

{{-- Open Graph --}}
<meta property="og:image" content="{{ $og_image }}">
<meta property="og:url" content="{{ $site_url }}">
```

## Settings Configuration

### في لوحة التحكم
1. اذهب إلى الإعدادات
2. اختر تبويب "SEO Settings"
3. املأ الحقول التالية:
   - Meta Title (متعدد اللغات)
   - Meta Description (متعدد اللغات)  
   - Meta Keywords (متعدد اللغات)
   - Google Analytics ID
   - Google Tag Manager ID
   - Facebook Pixel ID
   - Custom Head Code
   - Custom Body Code
   - Robots.txt Content

### Database Fields
جميع الحقول موجودة في جدول `settings`:

```sql
-- SEO Fields
meta_title (JSON)
meta_description (JSON)
meta_keywords (JSON)
google_analytics_id (TEXT)
google_tag_manager_id (TEXT)
facebook_pixel_id (TEXT)
custom_head_code (TEXT)
custom_body_code (TEXT)
robots_txt (TEXT)
```

## Priority System

النظام يتبع أولوية في قراءة البيانات:

1. **البيانات المرسلة مباشرة للـ view**
2. **إعدادات الصفحة الرئيسية (front)** - إذا كانت موجودة
3. **إعدادات النظام (Settings)** - القيم الافتراضية
4. **القيم الثابتة** - كـ fallback أخير

## Robots.txt
- يقرأ المحتوى من إعدادات النظام
- إذا كان فارغاً، يستخدم المحتوى الافتراضي
- متاح على `/robots.txt`

## Caching
- جميع الإعدادات محفوظة في cache لمدة ساعة
- يتم مسح الـ cache تلقائياً عند التحديث
- يمكن مسح الـ cache يدوياً: `SettingsHelper::clearCache()`

## Testing
للتأكد من عمل النظام:

1. تحقق من source code الصفحة
2. ابحث عن meta tags في head
3. تأكد من وجود كود التتبع
4. اختبر `/robots.txt`
5. تحقق من Open Graph في Facebook Debugger

## Notes
- جميع الحقول متعددة اللغات تدعم العربية والإنجليزية
- الصور تُقرأ من الهوم أولاً ثم من الإعدادات
- النظام متوافق مع جميع محركات البحث
- يدعم Google Analytics 4 و Universal Analytics
