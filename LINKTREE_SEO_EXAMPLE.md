# LinkTree SEO Implementation Example

## ما تم تنفيذه:

### 1. تحديث Layout Guest
- تم تحديث `layouts/guest.blade.php` للتحقق من وجود بيانات LinkTree
- إذا كانت الصفحة LinkTree، يتم استخدام بيانات LinkTree للـ SEO
- إذا لم تكن LinkTree، يتم استخدام الإعدادات العامة

### 2. إنشاء Component خاص بـ LinkTree
- `components/linktree-seo.blade.php` - component مخصص لـ SEO الخاص بـ LinkTree
- يحتوي على جميع meta tags المطلوبة
- JSON-LD structured data للأشخاص

### 3. تحديث Controller
- تم تحديث `LinkTree.php` controller لتمرير بيانات LinkTree للـ layout

## البيانات المستخدمة من LinkTree:

### Meta Tags:
- **Title**: `{name} - {site_name}`
- **Description**: `{description}` أو `{job_title}` إذا لم يوجد وصف
- **Keywords**: `{keywords}` حسب اللغة الحالية
- **Author**: `{name}`
- **Image**: `{avatar}` أو `{cover}` أو الصورة الافتراضية

### Open Graph:
- `og:type` = "profile"
- `og:title` = عنوان الصفحة
- `og:description` = وصف LinkTree
- `og:image` = صورة الملف الشخصي
- `og:url` = رابط الصفحة الحالي
- `profile:first_name` = اسم صاحب LinkTree
- `profile:username` = slug الخاص بـ LinkTree

### Twitter Cards:
- `twitter:card` = "summary_large_image"
- `twitter:creator` = "@{slug}"
- جميع البيانات الأساسية

### JSON-LD Structured Data:
```json
{
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "اسم الشخص",
    "description": "الوصف",
    "url": "رابط الصفحة",
    "identifier": "slug",
    "jobTitle": "المسمى الوظيفي",
    "image": "صورة الملف الشخصي",
    "sameAs": ["روابط وسائل التواصل"],
    "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "رابط الصفحة"
    }
}
```

## مثال على النتيجة النهائية:

عندما يزور المستخدم صفحة LinkTree مثل `/linktree/john-doe`، ستظهر في source code الصفحة:

```html
<title>John Doe - موقعي</title>
<meta name="description" content="مطور ويب متخصص في Laravel و Vue.js">
<meta name="keywords" content="مطور, ويب, Laravel, Vue.js">
<meta name="author" content="John Doe">

<meta property="og:title" content="John Doe - موقعي">
<meta property="og:description" content="مطور ويب متخصص في Laravel و Vue.js">
<meta property="og:image" content="https://example.com/storage/avatars/john.jpg">
<meta property="og:type" content="profile">
<meta property="profile:first_name" content="John Doe">
<meta property="profile:username" content="john-doe">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:creator" content="@john-doe">

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "John Doe",
    "description": "مطور ويب متخصص في Laravel و Vue.js",
    "jobTitle": "مطور ويب",
    "image": "https://example.com/storage/avatars/john.jpg",
    "sameAs": [
        "https://github.com/johndoe",
        "https://linkedin.com/in/johndoe"
    ]
}
</script>
```

## الفوائد:

1. **محركات البحث**: ستفهم محركات البحث أن هذه صفحة شخص وليس موقع عادي
2. **وسائل التواصل**: عند مشاركة الرابط، ستظهر معلومات الشخص بدلاً من معلومات الموقع العامة
3. **SEO محسن**: كل صفحة LinkTree لها SEO مخصص حسب بيانات صاحبها
4. **Structured Data**: محركات البحث ستعرض معلومات غنية في نتائج البحث

## الاستخدام:

الكود يعمل تلقائياً! عندما يتم عرض صفحة LinkTree:
1. يتم تمرير بيانات LinkTree للـ layout
2. Layout يتحقق من وجود البيانات
3. إذا وُجدت، يستخدم component LinkTree SEO
4. إذا لم توجد، يستخدم SEO العام

لا حاجة لتعديل أي شيء آخر - النظام يعمل تلقائياً! 🎉
